"""
Test Framework with Sample Data

This script creates sample Bronze layer data and tests the complete
data quality framework workflow. Perfect for testing and demonstration.
"""

import os
import tempfile
import shutil
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any
from pyspark.sql import SparkSession
from pyspark.sql.types import StructType, StructField, StringType, IntegerType, DoubleType, TimestampType
from pyspark.sql.functions import col, lit, when, rand, expr
import uuid

# Import framework components
from src.data_testing_framework import DataTestingFramework
from src.utils.logging_config import configure_logging


class SampleDataGenerator:
    """Generates sample Bronze layer data for testing"""
    
    def __init__(self, spark: SparkSession):
        self.spark = spark
        
    def create_good_quality_data(self, num_records: int = 1000) -> Dict[str, Any]:
        """Create high-quality sample data that should pass all tests"""
        
        # Define schema
        schema = StructType([
            StructField("order_id", StringType(), False),
            StructField("customer_id", StringType(), False),
            StructField("product_id", StringType(), False),
            StructField("product_name", StringType(), True),
            StructField("category", StringType(), True),
            StructField("quantity", IntegerType(), True),
            StructField("unit_price", DoubleType(), True),
            StructField("total_amount", DoubleType(), True),
            StructField("order_date", StringType(), True),
            StructField("customer_email", StringType(), True),
            StructField("shipping_address", StringType(), True),
            StructField("ingestion_timestamp", TimestampType(), False),
            StructField("source_file", StringType(), True),
            StructField("batch_id", StringType(), True)
        ])
        
        # Create sample data
        data = []
        base_time = datetime.now() - timedelta(minutes=30)  # Recent data
        
        for i in range(num_records):
            order_id = f"ORD-{i+1:06d}"
            customer_id = f"CUST-{(i % 100) + 1:04d}"
            product_id = f"PROD-{(i % 50) + 1:03d}"
            
            data.append({
                "order_id": order_id,
                "customer_id": customer_id,
                "product_id": product_id,
                "product_name": f"Product {product_id}",
                "category": ["Electronics", "Clothing", "Books", "Home", "Sports"][i % 5],
                "quantity": (i % 5) + 1,
                "unit_price": round(10.0 + (i % 100), 2),
                "total_amount": round((10.0 + (i % 100)) * ((i % 5) + 1), 2),
                "order_date": (base_time - timedelta(days=i % 7)).strftime("%Y-%m-%d"),
                "customer_email": f"customer{(i % 100) + 1}@email.com",
                "shipping_address": f"{i+1} Main St, City, State",
                "ingestion_timestamp": base_time + timedelta(seconds=i),
                "source_file": f"orders_batch_{(i // 100) + 1}.json",
                "batch_id": "BATCH-001"
            })
        
        df = self.spark.createDataFrame(data, schema)
        
        return {
            "dataframe": df,
            "schema": schema,
            "record_count": num_records,
            "metadata": {
                "source_system": "ecommerce_api",
                "extraction_time": base_time.isoformat(),
                "file_count": (num_records // 100) + 1,
                "source_format": "json",
                "data_date": base_time.strftime("%Y-%m-%d"),
                "batch_id": "BATCH-001"
            }
        }
    
    def create_poor_quality_data(self, num_records: int = 1000) -> Dict[str, Any]:
        """Create poor-quality sample data that should trigger warnings/failures"""
        
        good_data = self.create_good_quality_data(num_records)
        df = good_data["dataframe"]
        
        # Introduce quality issues
        
        # 1. Missing values (completeness issue)
        df = df.withColumn("product_name", 
                          when(rand() < 0.1, None).otherwise(col("product_name")))
        
        df = df.withColumn("customer_email", 
                          when(rand() < 0.05, None).otherwise(col("customer_email")))
        
        # 2. Invalid data (accuracy issue)
        df = df.withColumn("quantity", 
                          when(rand() < 0.02, -1).otherwise(col("quantity")))
        
        df = df.withColumn("unit_price", 
                          when(rand() < 0.02, -10.0).otherwise(col("unit_price")))
        
        # 3. Inconsistent data
        df = df.withColumn("total_amount", 
                          when(rand() < 0.03, 999999.99).otherwise(col("total_amount")))
        
        # 4. Old timestamps (timeliness issue)
        old_timestamp = datetime.now() - timedelta(days=5)
        df = df.withColumn("ingestion_timestamp", 
                          when(rand() < 0.1, lit(old_timestamp)).otherwise(col("ingestion_timestamp")))
        
        # 5. Add some duplicates (uniqueness issue)
        duplicate_rows = df.limit(int(num_records * 0.02))  # 2% duplicates
        df = df.union(duplicate_rows)
        
        good_data["dataframe"] = df
        good_data["record_count"] = df.count()
        
        return good_data
    
    def create_corrupted_data(self, num_records: int = 1000) -> Dict[str, Any]:
        """Create severely corrupted data that should trigger failures"""
        
        good_data = self.create_good_quality_data(num_records)
        df = good_data["dataframe"]
        
        # Severe corruption issues
        
        # 1. Massive missing values (50% missing)
        df = df.withColumn("order_id", 
                          when(rand() < 0.5, None).otherwise(col("order_id")))
        
        df = df.withColumn("customer_id", 
                          when(rand() < 0.3, None).otherwise(col("customer_id")))
        
        # 2. Wrong data types / invalid values
        df = df.withColumn("quantity", 
                          when(rand() < 0.2, -999).otherwise(col("quantity")))
        
        # 3. Very old data (timeliness failure)
        very_old_timestamp = datetime.now() - timedelta(days=30)
        df = df.withColumn("ingestion_timestamp", lit(very_old_timestamp))
        
        # 4. Massive duplicates (20% duplicates)
        duplicate_rows = df.limit(int(num_records * 0.2))
        df = df.union(duplicate_rows)
        
        good_data["dataframe"] = df
        good_data["record_count"] = df.count()
        
        return good_data


def create_test_config(email_enabled: bool = False) -> Dict[str, Any]:
    """Create test configuration"""
    return {
        "environment": {
            "name": "test",
            "catalog_name": "test_catalog",
            "schema_name": "bronze"
        },
        "bronze_layer": {
            "tests": {
                "file_integrity": True,
                "schema_validation": True,
                "record_counting": True,
                "timestamp_validation": True,
                "metadata_preservation": True,
                "duplicate_detection": True
            },
            "thresholds": {
                "completeness_min": 0.95,
                "accuracy_min": 0.98,
                "consistency_min": 0.95,
                "validity_min": 0.98,
                "timeliness_hours": 24,
                "uniqueness_min": 0.98,
                "duplicate_threshold": 0.05
            },
            "file_processing": {
                "max_file_size_gb": 10,
                "supported_formats": ["parquet", "delta", "json", "csv"],
                "encoding": "utf-8"
            }
        },
        "quality_gates": {
            "pass_threshold": 0.95,
            "warning_threshold": 0.85,
            "quarantine": {
                "enabled": True,
                "storage_path": "/tmp/quarantine",
                "retention_days": 30
            },
            "escalation": {
                "emergency_contact_threshold": 0.5,
                "auto_recovery_enabled": False
            }
        },
        "notifications": {
            "email": {
                "enabled": email_enabled,
                "smtp_server": "localhost",
                "smtp_port": 587,
                "smtp_username": "<EMAIL>",
                "smtp_password": "test_password",
                "from_address": "<EMAIL>",
                "recipients": {
                    "critical": ["<EMAIL>"],
                    "warning": ["<EMAIL>"],
                    "info": ["<EMAIL>"]
                }
            },
            "slack": {
                "enabled": False
            }
        },
        "logging": {
            "level": "INFO",
            "format": "console",
            "destinations": {
                "console": True,
                "file": False,
                "databricks": False
            }
        },
        "performance": {
            "spark": {
                "executor_memory": "2g",
                "executor_cores": 2,
                "max_result_size": "1g"
            }
        }
    }


def test_scenario(scenario_name: str, data_generator_func, expected_decision: str, spark: SparkSession):
    """Test a specific data quality scenario"""
    
    print(f"\n{'='*60}")
    print(f"🧪 TESTING SCENARIO: {scenario_name}")
    print(f"{'='*60}")
    
    # Generate sample data
    print("📊 Generating sample data...")
    sample_data = data_generator_func(1000)
    df = sample_data["dataframe"]
    
    print(f"✅ Generated {sample_data['record_count']} records")
    print(f"📋 Schema: {len(df.columns)} columns")
    
    # Save to temporary location
    temp_dir = tempfile.mkdtemp()
    table_path = os.path.join(temp_dir, "test_table")
    
    try:
        # Write as Delta table
        df.write.format("delta").mode("overwrite").save(table_path)
        print(f"💾 Saved test data to: {table_path}")
        
        # Initialize framework
        config = create_test_config(email_enabled=False)  # Disable email for testing
        framework = DataTestingFramework(config_dict=config, spark=spark)
        
        # Run tests
        print("🔍 Running data quality tests...")
        results = framework.run_bronze_pipeline_tests(
            table_path=table_path,
            expected_schema=sample_data["schema"],
            expected_count=1000,  # Expected count
            source_metadata=sample_data["metadata"],
            timestamp_column="ingestion_timestamp",
            send_notifications=False  # Disable notifications for testing
        )
        
        # Display results
        print(f"\n📊 TEST RESULTS:")
        print(f"   Execution ID: {results['execution_id']}")
        print(f"   Execution Time: {results['execution_time_seconds']:.2f} seconds")
        
        quality_gate = results['quality_gate']
        decision_emoji = {
            "PASS": "✅",
            "WARNING": "⚠️", 
            "FAIL": "❌",
            "QUARANTINE": "🔒",
            "EMERGENCY": "🚨"
        }
        
        actual_decision = quality_gate['decision']
        emoji = decision_emoji.get(actual_decision, "❓")
        
        print(f"\n{emoji} Quality Gate Decision: {actual_decision}")
        print(f"📈 Overall Score: {quality_gate['overall_score']:.2%}")
        print(f"🎯 Expected Decision: {expected_decision}")
        print(f"✅ Test Result: {'PASS' if actual_decision == expected_decision else 'UNEXPECTED'}")
        
        # Test summary
        test_summary = results['test_summary']
        print(f"\n📋 Test Summary:")
        print(f"   Total Tests: {test_summary['total_tests']}")
        print(f"   Passed: {test_summary['passed']}")
        print(f"   Warnings: {test_summary['warnings']}")
        print(f"   Failed: {test_summary['failed']}")
        print(f"   Errors: {test_summary['errors']}")
        
        # Individual test results
        print(f"\n🔍 Individual Test Results:")
        for test_result in results['test_suite']['test_results']:
            test_name = test_result['test_name']
            status = test_result['status']
            score = test_result.get('overall_score', 0)
            duration = test_result['execution_time_seconds']
            
            status_emoji = {"PASS": "✅", "WARNING": "⚠️", "FAIL": "❌", "ERROR": "💥"}
            print(f"   {status_emoji.get(status, '❓')} {test_name}: {status} ({score:.1%}) [{duration:.2f}s]")
        
        # Recommendations
        if quality_gate['recommendations']:
            print(f"\n💡 Recommendations:")
            for i, rec in enumerate(quality_gate['recommendations'], 1):
                print(f"   {i}. {rec}")
        
        # Next actions
        print(f"\n🎯 Next Actions:")
        for i, action in enumerate(results['next_actions'], 1):
            print(f"   {i}. {action}")
        
        # Pipeline decision
        print(f"\n🚦 Pipeline Decision:")
        if actual_decision in ['PASS', 'WARNING']:
            print("   ✅ PROCEED to Silver layer processing")
        elif actual_decision in ['FAIL', 'QUARANTINE']:
            print("   ❌ BLOCK pipeline - fix issues before proceeding")
        elif actual_decision == 'EMERGENCY':
            print("   🚨 HALT all processing - immediate escalation required")
        
        return results
        
    finally:
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)


def main():
    """Main testing function"""
    
    print("🚀 MEDALLION ARCHITECTURE DATA TESTING FRAMEWORK")
    print("🧪 COMPREHENSIVE TESTING WITH SAMPLE DATA")
    print("="*60)
    
    # Configure logging
    configure_logging({
        "level": "INFO",
        "format": "console",
        "destinations": {"console": True, "file": False}
    })
    
    # Initialize Spark
    print("⚡ Initializing Spark session...")
    spark = SparkSession.builder \
        .appName("DataQualityFrameworkTest") \
        .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension") \
        .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog") \
        .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer") \
        .getOrCreate()
    
    print(f"✅ Spark {spark.version} initialized")
    
    # Initialize data generator
    data_generator = SampleDataGenerator(spark)
    
    try:
        # Test Scenario 1: High Quality Data (should PASS)
        test_scenario(
            "High Quality Data", 
            data_generator.create_good_quality_data,
            "PASS",
            spark
        )
        
        # Test Scenario 2: Poor Quality Data (should WARNING or FAIL)
        test_scenario(
            "Poor Quality Data",
            data_generator.create_poor_quality_data, 
            "WARNING",  # Might be FAIL depending on thresholds
            spark
        )
        
        # Test Scenario 3: Corrupted Data (should FAIL or QUARANTINE)
        test_scenario(
            "Corrupted Data",
            data_generator.create_corrupted_data,
            "FAIL",  # Might be QUARANTINE or EMERGENCY
            spark
        )
        
        print(f"\n{'='*60}")
        print("🎉 ALL TESTS COMPLETED!")
        print("✅ Framework is working correctly")
        print("📚 Check the results above to understand the quality decisions")
        print("🔧 Adjust thresholds in config if needed")
        print("="*60)
        
    finally:
        spark.stop()
        print("🧹 Spark session stopped")


if __name__ == "__main__":
    main()
