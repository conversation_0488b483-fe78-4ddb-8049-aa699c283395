#!/usr/bin/env python3
"""
Example: Using Data Quality Framework with DataFrames

This example shows how to use the data quality framework with any DataFrame,
making it entity-agnostic and reusable across different data sources.
"""

import json
from pyspark.sql import SparkSession
from pyspark.sql.types import StructType, StructField, StringType, IntegerType, DoubleType, TimestampType
from pyspark.sql.functions import col, current_timestamp, lit
from datetime import datetime, timedelta

# Import the data quality function
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from data_quality_check import run_data_quality_tests


def create_orders_dataframe(spark: SparkSession, quality_level: str = "good") -> tuple:
    """Create a sample orders DataFrame with different quality levels"""
    
    # Define schema
    schema = StructType([
        StructField("order_id", StringType(), False),
        StructField("customer_id", StringType(), False),
        StructField("product_id", StringType(), False),
        StructField("quantity", IntegerType(), False),
        StructField("unit_price", DoubleType(), False),
        StructField("total_amount", DoubleType(), False),
        StructField("order_date", TimestampType(), False),
        StructField("customer_email", StringType(), True),
        StructField("product_category", StringType(), True),
        StructField("ingestion_timestamp", TimestampType(), False)
    ])
    
    # Create sample data
    base_time = datetime.now() - timedelta(hours=1)
    data = []
    
    for i in range(1000):
        data.append((
            f"ORD-{i:06d}",
            f"CUST-{i % 100:04d}",
            f"PROD-{i % 50:03d}",
            (i % 5) + 1,
            round(10.0 + (i % 100), 2),
            round((10.0 + (i % 100)) * ((i % 5) + 1), 2),
            base_time + timedelta(minutes=i % 60),
            f"customer{i % 100}@example.com",
            ["Electronics", "Clothing", "Books", "Home", "Sports"][i % 5],
            datetime.now()
        ))
    
    df = spark.createDataFrame(data, schema)
    
    # Apply quality issues based on level
    if quality_level == "poor":
        # Introduce moderate issues
        df = df.withColumn("customer_email", 
                          col("customer_email").cast("string"))  # Keep as is
        df = df.withColumn("quantity", 
                          col("quantity").cast("int"))  # Some nulls will appear
        
    elif quality_level == "bad":
        # Introduce severe issues
        df = df.withColumn("order_id", 
                          col("order_id").cast("string"))  # Some nulls
        df = df.withColumn("customer_id", 
                          col("customer_id").cast("string"))  # Some nulls
    
    return df, schema


def create_customers_dataframe(spark: SparkSession) -> tuple:
    """Create a sample customers DataFrame"""
    
    schema = StructType([
        StructField("customer_id", StringType(), False),
        StructField("first_name", StringType(), False),
        StructField("last_name", StringType(), False),
        StructField("email", StringType(), False),
        StructField("phone", StringType(), True),
        StructField("registration_date", TimestampType(), False),
        StructField("ingestion_timestamp", TimestampType(), False)
    ])
    
    # Create sample data
    data = []
    for i in range(500):
        data.append((
            f"CUST-{i:04d}",
            f"FirstName{i}",
            f"LastName{i}",
            f"customer{i}@example.com",
            f"******-{i:04d}" if i % 3 == 0 else None,
            datetime.now() - timedelta(days=i % 365),
            datetime.now()
        ))
    
    df = spark.createDataFrame(data, schema)
    return df, schema


def load_expected_schema(schema_file: str) -> StructType:
    """Load expected schema from JSON file"""
    with open(schema_file, 'r') as f:
        schema_dict = json.load(f)
    return StructType.fromJson(schema_dict)


def main():
    """Main example function"""
    
    # Initialize Spark
    spark = SparkSession.builder \
        .appName("DataQualityExample") \
        .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer") \
        .getOrCreate()
    
    print("🚀 DATA QUALITY FRAMEWORK - DATAFRAME USAGE EXAMPLES")
    print("=" * 60)
    
    # Example 1: Orders DataFrame with expected schema
    print("\n📦 EXAMPLE 1: Orders DataFrame with JSON Schema Validation")
    print("-" * 50)
    
    orders_df, orders_schema = create_orders_dataframe(spark, "good")
    expected_orders_schema = load_expected_schema("examples/orders_schema.json")
    
    result1 = run_data_quality_tests(
        df=orders_df,
        expected_count=1000,
        expected_schema=expected_orders_schema,
        table_name="orders",
        batch_id="batch_orders_001",
        source_system="ecommerce_api",
        fail_on_warning=False,
        send_notifications=False,
        spark=spark
    )
    
    print(f"Result: {result1['status']} - {result1['message']}")
    
    # Example 2: Customers DataFrame without expected schema
    print("\n👥 EXAMPLE 2: Customers DataFrame (Basic Validation)")
    print("-" * 50)
    
    customers_df, customers_schema = create_customers_dataframe(spark)
    
    result2 = run_data_quality_tests(
        df=customers_df,
        expected_count=500,
        table_name="customers",
        batch_id="batch_customers_001",
        source_system="crm_system",
        fail_on_warning=True,
        send_notifications=False,
        spark=spark
    )
    
    print(f"Result: {result2['status']} - {result2['message']}")
    
    # Example 3: Poor quality data
    print("\n⚠️  EXAMPLE 3: Poor Quality Orders Data")
    print("-" * 50)
    
    poor_orders_df, _ = create_orders_dataframe(spark, "poor")
    
    result3 = run_data_quality_tests(
        df=poor_orders_df,
        expected_count=1000,
        expected_schema=expected_orders_schema,
        table_name="orders_poor",
        batch_id="batch_orders_poor_001",
        source_system="ecommerce_api",
        fail_on_warning=False,
        send_notifications=False,
        spark=spark
    )
    
    print(f"Result: {result3['status']} - {result3['message']}")
    
    # Example 4: Integration with pipeline control table simulation
    print("\n📋 EXAMPLE 4: Simulated Pipeline Control Table")
    print("-" * 50)
    
    # Simulate loading from control table
    control_config = {
        "expected_count": 1000,
        "expected_schema": expected_orders_schema,
        "fail_on_warning": True
    }
    
    result4 = run_data_quality_tests(
        df=orders_df,
        expected_count=control_config["expected_count"],
        expected_schema=control_config["expected_schema"],
        table_name="orders_controlled",
        batch_id="batch_orders_controlled_001",
        source_system="ecommerce_api",
        fail_on_warning=control_config["fail_on_warning"],
        send_notifications=False,
        spark=spark
    )
    
    print(f"Result: {result4['status']} - {result4['message']}")
    
    print("\n✅ All examples completed!")
    print("\n📊 SUMMARY:")
    print(f"  Example 1 (Good Orders): {result1['quality_decision']} ({result1['overall_score']:.1%})")
    print(f"  Example 2 (Customers): {result2['quality_decision']} ({result2['overall_score']:.1%})")
    print(f"  Example 3 (Poor Orders): {result3['quality_decision']} ({result3['overall_score']:.1%})")
    print(f"  Example 4 (Controlled): {result4['quality_decision']} ({result4['overall_score']:.1%})")
    
    spark.stop()


if __name__ == "__main__":
    main()
