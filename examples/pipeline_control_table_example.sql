-- Example Pipeline Control Table Structure
-- This table stores expected counts and schemas for each Bronze layer table

CREATE TABLE IF NOT EXISTS main.control.pipeline_metadata (
    table_path STRING COMMENT 'Full path to the Bronze table (e.g., main.bronze.orders)',
    source_system STRING COMMENT 'Source system name',
    expected_count BIGINT COMMENT 'Expected number of records per batch',
    expected_count_tolerance DOUBLE COMMENT 'Tolerance percentage for count validation (0.05 = 5%)',
    expected_schema_json STRING COMMENT 'JSON representation of expected schema',
    data_freshness_hours INT COMMENT 'Maximum age of data in hours',
    critical_columns ARRAY<STRING> COMMENT 'Columns that cannot be null',
    business_rules STRING COMMENT 'JSON with business validation rules',
    active BOOLEAN COMMENT 'Whether this configuration is active',
    created_by STRING COMMENT 'User who created this configuration',
    created_at TIMESTAMP COMMENT 'When this configuration was created',
    updated_at TIMESTAMP COMMENT 'Last update timestamp'
) USING DELTA
COMMENT 'Pipeline control table for data quality testing configuration';

-- Example data for orders table
INSERT INTO main.control.pipeline_metadata VALUES (
    'main.bronze.orders',
    'ecommerce_api',
    1000,
    0.05,
    '{"type":"struct","fields":[
        {"name":"order_id","type":"string","nullable":false,"metadata":{}},
        {"name":"customer_id","type":"string","nullable":false,"metadata":{}},
        {"name":"product_id","type":"string","nullable":false,"metadata":{}},
        {"name":"quantity","type":"integer","nullable":false,"metadata":{}},
        {"name":"unit_price","type":"double","nullable":false,"metadata":{}},
        {"name":"total_amount","type":"double","nullable":false,"metadata":{}},
        {"name":"order_date","type":"timestamp","nullable":false,"metadata":{}},
        {"name":"customer_email","type":"string","nullable":true,"metadata":{}},
        {"name":"product_category","type":"string","nullable":true,"metadata":{}},
        {"name":"ingestion_timestamp","type":"timestamp","nullable":false,"metadata":{}}
    ]}',
    24,
    array('order_id', 'customer_id', 'product_id', 'quantity'),
    '{"quantity_min": 1, "unit_price_min": 0.01, "email_format": "^[\\w\\.-]+@[\\w\\.-]+\\.[a-zA-Z]{2,}$"}',
    true,
    'data_engineer',
    current_timestamp(),
    current_timestamp()
);

-- Example data for customers table
INSERT INTO main.control.pipeline_metadata VALUES (
    'main.bronze.customers',
    'crm_system',
    500,
    0.10,
    '{"type":"struct","fields":[
        {"name":"customer_id","type":"string","nullable":false,"metadata":{}},
        {"name":"first_name","type":"string","nullable":false,"metadata":{}},
        {"name":"last_name","type":"string","nullable":false,"metadata":{}},
        {"name":"email","type":"string","nullable":false,"metadata":{}},
        {"name":"phone","type":"string","nullable":true,"metadata":{}},
        {"name":"registration_date","type":"timestamp","nullable":false,"metadata":{}},
        {"name":"ingestion_timestamp","type":"timestamp","nullable":false,"metadata":{}}
    ]}',
    12,
    array('customer_id', 'email'),
    '{"email_format": "^[\\w\\.-]+@[\\w\\.-]+\\.[a-zA-Z]{2,}$"}',
    true,
    'data_engineer',
    current_timestamp(),
    current_timestamp()
);
