"""
Basic Usage Example

This example demonstrates how to use the Medallion Architecture Data Testing Framework
for Bronze layer data quality testing.
"""

import os
from pyspark.sql import SparkSession
from pyspark.sql.types import StructType, StructField, StringType, IntegerType, TimestampType

# Import the framework
from src.data_testing_framework import DataTestingFramework
from src.utils.logging_config import configure_logging


def main():
    """Basic usage example"""
    
    # Configure logging
    configure_logging({
        "level": "INFO",
        "format": "console",
        "destinations": {
            "console": True,
            "file": False
        }
    })
    
    # Initialize Spark session
    spark = SparkSession.builder \
        .appName("DataQualityTestingExample") \
        .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension") \
        .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog") \
        .getOrCreate()
    
    # Initialize the framework with configuration
    config_path = "config/config.yaml"  # Update with your config path
    framework = DataTestingFramework(config_path=config_path, spark=spark)
    
    # Validate configuration
    print("Validating configuration...")
    validation_result = framework.validate_configuration()
    if not validation_result["config_valid"]:
        print("Configuration validation failed:")
        for issue in validation_result["issues"]:
            print(f"  - {issue}")
        return
    
    print("Configuration is valid!")
    
    # Define expected schema for your Bronze table
    expected_schema = StructType([
        StructField("customer_id", StringType(), False),
        StructField("order_id", StringType(), False),
        StructField("product_name", StringType(), True),
        StructField("quantity", IntegerType(), True),
        StructField("price", StringType(), True),
        StructField("order_date", StringType(), True),
        StructField("ingestion_timestamp", TimestampType(), False),
        StructField("source_file", StringType(), True)
    ])
    
    # Source metadata (information about the source system)
    source_metadata = {
        "source_system": "ecommerce_orders",
        "extraction_time": "2024-01-15T10:30:00Z",
        "file_count": 5,
        "source_format": "csv",
        "data_date": "2024-01-15"
    }
    
    # Run Bronze layer tests
    print("Running Bronze layer data quality tests...")
    
    table_path = "/path/to/your/bronze/table"  # Update with your table path
    
    results = framework.run_bronze_pipeline_tests(
        table_path=table_path,
        expected_schema=expected_schema,
        expected_count=10000,  # Expected number of records
        source_metadata=source_metadata,
        timestamp_column="ingestion_timestamp",
        send_notifications=True
    )
    
    # Print results
    print("\n" + "="*50)
    print("DATA QUALITY TEST RESULTS")
    print("="*50)
    
    print(f"Execution ID: {results['execution_id']}")
    print(f"Table Path: {results['table_path']}")
    print(f"Execution Time: {results['execution_time_seconds']:.2f} seconds")
    
    # Quality Gate Decision
    quality_gate = results['quality_gate']
    print(f"\nQuality Gate Decision: {quality_gate['decision']}")
    print(f"Overall Score: {quality_gate['overall_score']:.2%}")
    print(f"Threshold Met: {quality_gate['threshold_met']}")
    
    if quality_gate['quarantine_required']:
        print("⚠️  QUARANTINE REQUIRED")
    
    if quality_gate['emergency_escalation']:
        print("🚨 EMERGENCY ESCALATION TRIGGERED")
    
    # Test Summary
    test_summary = results['test_summary']
    print(f"\nTest Summary:")
    print(f"  Total Tests: {test_summary['total_tests']}")
    print(f"  Passed: {test_summary['passed']}")
    print(f"  Warnings: {test_summary['warnings']}")
    print(f"  Failed: {test_summary['failed']}")
    print(f"  Errors: {test_summary['errors']}")
    
    # Recommendations
    if quality_gate['recommendations']:
        print(f"\nRecommendations:")
        for i, rec in enumerate(quality_gate['recommendations'], 1):
            print(f"  {i}. {rec}")
    
    # Next Actions
    print(f"\nNext Actions:")
    for i, action in enumerate(results['next_actions'], 1):
        print(f"  {i}. {action}")
    
    # Notification Results
    if results['notifications']:
        print(f"\nNotification Results:")
        for channel, success in results['notifications'].items():
            status = "✅ Sent" if success else "❌ Failed"
            print(f"  {channel.title()}: {status}")
    
    print("\n" + "="*50)
    
    # Test notifications (optional)
    print("\nTesting notification configuration...")
    notification_test = framework.test_notifications("all")
    if notification_test['success']:
        print("✅ Test notifications sent successfully")
    else:
        print("❌ Test notifications failed")
    
    # Get framework status
    print("\nFramework Status:")
    status = framework.get_framework_status()
    print(f"  Version: {status['framework_version']}")
    print(f"  Environment: {status['environment']}")
    print(f"  Spark Version: {status['spark_version']}")
    
    spark.stop()


if __name__ == "__main__":
    main()
