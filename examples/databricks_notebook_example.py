# Databricks notebook source
"""
Databricks Notebook Example

This notebook demonstrates how to integrate the Data Testing Framework
into a Databricks pipeline workflow.

Run this after your Bronze layer ETL pipeline to validate data quality.
"""

# COMMAND ----------

# MAGIC %md
# MAGIC # Medallion Architecture Data Quality Testing
# MAGIC 
# MAGIC This notebook runs data quality tests on Bronze layer tables after ETL pipeline execution.

# COMMAND ----------

# Install required packages (if not already installed)
# %pip install pydantic structlog jinja2 requests

# COMMAND ----------

# Import required libraries
import os
from datetime import datetime
from pyspark.sql import SparkSession
from pyspark.sql.types import StructType, StructField, StringType, IntegerType, TimestampType, DoubleType

# COMMAND ----------

# MAGIC %md
# MAGIC ## Configuration Setup

# COMMAND ----------

# Configuration for the data testing framework
# In production, this would come from a configuration file or Databricks secrets

config = {
    "environment": {
        "name": "production",
        "catalog_name": "main",
        "schema_name": "bronze"
    },
    "bronze_layer": {
        "tests": {
            "file_integrity": True,
            "schema_validation": True,
            "record_counting": True,
            "timestamp_validation": True,
            "metadata_preservation": True,
            "duplicate_detection": True
        },
        "thresholds": {
            "completeness_min": 0.95,
            "accuracy_min": 0.98,
            "timeliness_hours": 24,
            "duplicate_threshold": 0.05
        }
    },
    "quality_gates": {
        "pass_threshold": 0.95,
        "warning_threshold": 0.85,
        "quarantine": {
            "enabled": True,
            "storage_path": "/mnt/quarantine/bronze"
        },
        "escalation": {
            "emergency_contact_threshold": 0.5
        }
    },
    "notifications": {
        "email": {
            "enabled": True,
            "smtp_server": "smtp.company.com",
            "smtp_port": 587,
            "from_address": "<EMAIL>",
            "recipients": {
                "critical": ["<EMAIL>", "<EMAIL>"],
                "warning": ["<EMAIL>"],
                "info": ["<EMAIL>"]
            }
        },
        "slack": {
            "enabled": True,
            "webhook_url": dbutils.secrets.get("data-quality", "slack-webhook"),
            "channel": "#data-quality-alerts",
            "username": "DataQualityBot",
            "mention_on_critical": True,
            "critical_mentions": ["@data-team", "@on-call"],
            "include_details": True
        }
    },
    "logging": {
        "level": "INFO",
        "destinations": {
            "console": True,
            "databricks": True
        },
        "databricks_logging": {
            "log_table": "main.logs.data_quality_logs"
        }
    }
}

# COMMAND ----------

# MAGIC %md
# MAGIC ## Initialize Framework

# COMMAND ----------

# Import the framework (assuming it's installed or available in the workspace)
from src.data_testing_framework import DataTestingFramework
from src.utils.logging_config import configure_logging

# Configure logging for Databricks
configure_logging(config["logging"])

# Initialize the framework
framework = DataTestingFramework(config_dict=config)

print("✅ Data Testing Framework initialized")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Define Test Parameters

# COMMAND ----------

# Parameters for the Bronze table to test
# These would typically come from the ETL pipeline or be parameterized

# Table information
TABLE_PATH = "/mnt/bronze/ecommerce/orders"  # Update with your table path
CATALOG_TABLE = "main.bronze.ecommerce_orders"  # Unity Catalog table name

# Expected schema
EXPECTED_SCHEMA = StructType([
    StructField("order_id", StringType(), False),
    StructField("customer_id", StringType(), False),
    StructField("product_id", StringType(), False),
    StructField("product_name", StringType(), True),
    StructField("category", StringType(), True),
    StructField("quantity", IntegerType(), True),
    StructField("unit_price", DoubleType(), True),
    StructField("total_amount", DoubleType(), True),
    StructField("order_date", StringType(), True),
    StructField("customer_email", StringType(), True),
    StructField("shipping_address", StringType(), True),
    StructField("ingestion_timestamp", TimestampType(), False),
    StructField("source_file", StringType(), True),
    StructField("batch_id", StringType(), True)
])

# Expected record count (from source system or previous runs)
EXPECTED_RECORD_COUNT = 50000

# Source metadata
SOURCE_METADATA = {
    "source_system": "ecommerce_api",
    "extraction_timestamp": datetime.now().isoformat(),
    "api_version": "v2.1",
    "batch_id": dbutils.widgets.get("batch_id") if "batch_id" in [w.name for w in dbutils.widgets.getAll()] else "manual_run",
    "data_date": datetime.now().strftime("%Y-%m-%d")
}

print(f"Testing table: {TABLE_PATH}")
print(f"Expected records: {EXPECTED_RECORD_COUNT:,}")
print(f"Batch ID: {SOURCE_METADATA['batch_id']}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Run Data Quality Tests

# COMMAND ----------

# Run the complete Bronze layer testing workflow
print("🔍 Starting Bronze layer data quality tests...")

results = framework.run_bronze_pipeline_tests(
    table_path=TABLE_PATH,
    expected_schema=EXPECTED_SCHEMA,
    expected_count=EXPECTED_RECORD_COUNT,
    source_metadata=SOURCE_METADATA,
    timestamp_column="ingestion_timestamp",
    send_notifications=True
)

print("✅ Data quality tests completed!")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Display Results

# COMMAND ----------

# Display comprehensive results
print("=" * 60)
print("DATA QUALITY TEST RESULTS")
print("=" * 60)

print(f"📊 Execution ID: {results['execution_id']}")
print(f"📁 Table: {results['table_path']}")
print(f"⏱️  Execution Time: {results['execution_time_seconds']:.2f} seconds")
print(f"🕐 Completed: {results['end_time']}")

# Quality Gate Decision
quality_gate = results['quality_gate']
decision_emoji = {
    "PASS": "✅",
    "WARNING": "⚠️",
    "FAIL": "❌",
    "QUARANTINE": "🔒",
    "EMERGENCY": "🚨"
}

print(f"\n{decision_emoji.get(quality_gate['decision'], '📊')} Quality Gate: {quality_gate['decision']}")
print(f"📈 Overall Score: {quality_gate['overall_score']:.2%}")
print(f"🎯 Threshold Met: {'Yes' if quality_gate['threshold_met'] else 'No'}")

if quality_gate['quarantine_required']:
    print("🔒 QUARANTINE REQUIRED - Data moved to quarantine storage")

if quality_gate['emergency_escalation']:
    print("🚨 EMERGENCY ESCALATION - Immediate attention required!")

# COMMAND ----------

# Test Summary Table
test_summary = results['test_summary']

print(f"\n📋 TEST SUMMARY")
print(f"{'Metric':<20} {'Count':<10}")
print("-" * 30)
print(f"{'Total Tests':<20} {test_summary['total_tests']:<10}")
print(f"{'Passed':<20} {test_summary['passed']:<10}")
print(f"{'Warnings':<20} {test_summary['warnings']:<10}")
print(f"{'Failed':<20} {test_summary['failed']:<10}")
print(f"{'Errors':<20} {test_summary['errors']:<10}")

# COMMAND ----------

# Individual Test Results
print(f"\n🔍 INDIVIDUAL TEST RESULTS")
print(f"{'Test Name':<25} {'Status':<10} {'Score':<10} {'Duration':<10}")
print("-" * 65)

test_suite = results['test_suite']
for test_result in test_suite['test_results']:
    test_name = test_result['test_name']
    status = test_result['status']
    score = f"{test_result.get('overall_score', 0):.1%}"
    duration = f"{test_result['execution_time_seconds']:.2f}s"
    
    status_emoji = {"PASS": "✅", "WARNING": "⚠️", "FAIL": "❌", "ERROR": "💥"}
    status_display = f"{status_emoji.get(status, '❓')} {status}"
    
    print(f"{test_name:<25} {status_display:<10} {score:<10} {duration:<10}")

# COMMAND ----------

# Recommendations and Next Actions
if quality_gate['recommendations']:
    print(f"\n💡 RECOMMENDATIONS")
    for i, rec in enumerate(quality_gate['recommendations'], 1):
        print(f"  {i}. {rec}")

print(f"\n🎯 NEXT ACTIONS")
for i, action in enumerate(results['next_actions'], 1):
    print(f"  {i}. {action}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Notification Status

# COMMAND ----------

# Display notification results
if results.get('notifications'):
    print(f"\n📧 NOTIFICATION STATUS")
    for channel, success in results['notifications'].items():
        status = "✅ Sent" if success else "❌ Failed"
        print(f"  {channel.title()}: {status}")
else:
    print("\n📧 No notifications configured or sent")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Save Results to Delta Table

# COMMAND ----------

# Save results to a Delta table for historical tracking
results_df = spark.createDataFrame([{
    "execution_id": results['execution_id'],
    "table_path": results['table_path'],
    "execution_date": results['start_time'],
    "quality_decision": quality_gate['decision'],
    "overall_score": quality_gate['overall_score'],
    "threshold_met": quality_gate['threshold_met'],
    "total_tests": test_summary['total_tests'],
    "passed_tests": test_summary['passed'],
    "warning_tests": test_summary['warnings'],
    "failed_tests": test_summary['failed'],
    "execution_time_seconds": results['execution_time_seconds'],
    "quarantine_required": quality_gate['quarantine_required'],
    "emergency_escalation": quality_gate['emergency_escalation'],
    "batch_id": SOURCE_METADATA.get('batch_id'),
    "framework_version": results['framework_metadata']['version']
}])

# Write to Delta table
results_table = "main.logs.data_quality_results"
results_df.write.mode("append").option("mergeSchema", "true").saveAsTable(results_table)

print(f"✅ Results saved to {results_table}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Pipeline Decision Logic

# COMMAND ----------

# Based on the quality gate decision, determine if the pipeline should continue
decision = quality_gate['decision']

if decision == "PASS":
    print("✅ PIPELINE APPROVED: Proceed to Silver layer processing")
    dbutils.notebook.exit({"status": "success", "decision": "proceed"})
    
elif decision == "WARNING":
    print("⚠️ PIPELINE WARNING: Proceed with caution, monitor closely")
    dbutils.notebook.exit({"status": "warning", "decision": "proceed_with_caution"})
    
elif decision in ["FAIL", "QUARANTINE"]:
    print("❌ PIPELINE BLOCKED: Do not proceed to Silver layer")
    print("🔧 Action Required: Fix data quality issues before continuing")
    dbutils.notebook.exit({"status": "blocked", "decision": "stop"})
    
elif decision == "EMERGENCY":
    print("🚨 EMERGENCY: Immediate escalation required")
    print("🛑 All processing halted pending investigation")
    dbutils.notebook.exit({"status": "emergency", "decision": "halt_all"})

# COMMAND ----------

# MAGIC %md
# MAGIC ## Cleanup

# COMMAND ----------

print("🧹 Cleaning up resources...")
print("✅ Data quality testing completed successfully!")

# The framework automatically handles Spark session cleanup
