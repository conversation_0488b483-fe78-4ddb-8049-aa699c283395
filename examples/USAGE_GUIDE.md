# Data Quality Framework Usage Guide

## 🚀 Quick Start: DataFrame-Based Testing

The framework now supports **entity-agnostic testing** - you can pass any DataFrame and run quality tests on it:

```python
from data_quality_check import run_data_quality_tests
from pyspark.sql import SparkSession

# Initialize Spark
spark = SparkSession.builder.appName("DataQuality").getOrCreate()

# Your DataFrame (from any source)
df = spark.table("your_database.your_table")

# Run quality tests
result = run_data_quality_tests(
    df=df,
    expected_count=1000,
    expected_schema=your_expected_schema,
    table_name="orders",
    batch_id="batch_001",
    source_system="ecommerce_api"
)

# Check result
if result["continue_pipeline"]:
    print("✅ Quality checks passed - continue pipeline")
else:
    print(f"❌ Quality issues detected: {result['message']}")
```

## 🎯 **Record Count Expectations**

### Method 1: Pipeline Control Table (Recommended)

Create a control table to store expected counts and schemas:

```sql
-- Create control table
CREATE TABLE main.control.pipeline_metadata (
    table_path STRING,
    expected_count BIGINT,
    expected_schema_json STRING,
    -- ... other fields
);

-- Insert configuration
INSERT INTO main.control.pipeline_metadata VALUES (
    'main.bronze.orders',
    1000,
    '{"type":"struct","fields":[...]}',
    -- ... other values
);
```

**Usage:**
```bash
# Local
python data_quality_check.py \
    --table-path main.bronze.orders \
    --control-table main.control.pipeline_metadata

# Databricks
dbutils.widgets.text("table_path", "main.bronze.orders")
dbutils.widgets.text("control_table", "main.control.pipeline_metadata")
%run ./data_quality_check
```

### Method 2: Direct Parameter

**Usage:**
```bash
# Local
python data_quality_check.py \
    --table-path /path/to/table \
    --expected-count 1000

# Databricks
dbutils.widgets.text("table_path", "main.bronze.orders")
dbutils.widgets.text("expected_count", "1000")
%run ./data_quality_check
```

### Method 3: Dynamic from Source System

You can query the source system to get expected counts:

```python
# In your pipeline before calling data quality check
source_count = spark.sql("SELECT COUNT(*) FROM source_system.orders").collect()[0][0]

# Pass to quality check
dbutils.widgets.text("expected_count", str(source_count))
```

## 📋 **Schema Validation**

### Method 1: Control Table Schema (Recommended)

Store schema as JSON in control table:

```sql
INSERT INTO main.control.pipeline_metadata VALUES (
    'main.bronze.orders',
    1000,
    '{"type":"struct","fields":[
        {"name":"order_id","type":"string","nullable":false},
        {"name":"customer_id","type":"string","nullable":false},
        {"name":"quantity","type":"integer","nullable":false}
    ]}',
    -- ... other values
);
```

### Method 2: JSON Schema File

Create a JSON file with expected schema:

```json
{
  "type": "struct",
  "fields": [
    {
      "name": "order_id",
      "type": "string",
      "nullable": false,
      "metadata": {}
    },
    {
      "name": "customer_id", 
      "type": "string",
      "nullable": false,
      "metadata": {}
    }
  ]
}
```

**Usage:**
```bash
# Local
python data_quality_check.py \
    --table-path /path/to/table \
    --schema-json-path /path/to/schema.json

# Databricks
dbutils.widgets.text("schema_json_path", "/dbfs/schemas/orders_schema.json")
```

### Method 3: Extract from Reference Table

Extract schema from a reference table:

```python
# Get schema from reference table
reference_df = spark.table("main.gold.orders_clean")
expected_schema = reference_df.schema

# Convert to JSON for storage
schema_json = expected_schema.json()
```

## 🚀 **Complete Examples**

### Production Pipeline with Control Table

```python
# Databricks Workflow Task
dbutils.widgets.text("table_path", "main.bronze.orders")
dbutils.widgets.text("control_table", "main.control.pipeline_metadata")
dbutils.widgets.text("batch_id", "batch_20240701_143022")
dbutils.widgets.text("source_system", "ecommerce_api")
dbutils.widgets.text("fail_on_warning", "true")
dbutils.widgets.text("send_notifications", "true")

%run ./data_quality_check
```

### Local Testing with JSON Schema

```bash
python data_quality_check.py \
    --table-path /tmp/test_data/orders \
    --expected-count 1000 \
    --schema-json-path ./schemas/orders_schema.json \
    --batch-id test_batch_001 \
    --source-system test_system \
    --fail-on-warning \
    --send-notifications
```

### Sample Data Testing

```bash
# Test with good quality data
python data_quality_check.py --test-scenario good

# Test with poor quality data  
python data_quality_check.py --test-scenario poor

# Test with bad quality data
python data_quality_check.py --test-scenario bad
```

## 📊 **What Gets Validated**

### Record Count Validation
- ✅ **Exact match**: `actual_count == expected_count` → PASS
- ⚠️ **Close match**: `accuracy >= 95%` → WARNING  
- ❌ **Poor match**: `accuracy < 95%` → FAIL

### Schema Validation
- ✅ **Column existence**: All expected columns present
- ⚠️ **New columns**: Extra columns detected (configurable)
- ❌ **Missing columns**: Required columns missing
- ❌ **Type mismatches**: Column types don't match

### Data Quality Tests
- **File Integrity**: Can the data be read without errors?
- **Schema Validation**: Does structure match expectations?
- **Record Counting**: Are we getting expected volume?
- **Timestamp Validation**: Is data fresh enough?
- **Metadata Preservation**: Are lineage fields present?

## 🔧 **Configuration Priority**

1. **Control Table** (highest priority)
2. **JSON Schema File** 
3. **Direct Parameters** (lowest priority)

If multiple sources provide the same configuration, higher priority wins.
