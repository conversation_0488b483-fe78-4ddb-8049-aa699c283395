"""
Databricks Workflow Integration Example

This script shows how to integrate the Data Testing Framework
into Databricks workflows with proper error handling and
pipeline control flow.

Use this as a template for your Bronze layer data quality checks
in Databricks workflows.
"""

import sys
import json
from datetime import datetime
from typing import Dict, Any, Optional

# Databricks utilities (available in Databricks environment)
try:
    import dbutils
    DATABRICKS_ENV = True
except ImportError:
    # For local testing
    DATABRICKS_ENV = False
    class MockDbutils:
        class widgets:
            @staticmethod
            def get(name, default=""):
                return default
        class notebook:
            @staticmethod
            def exit(value):
                print(f"Notebook exit: {value}")
                sys.exit(0 if isinstance(value, dict) and value.get("status") == "success" else 1)
    dbutils = MockDbutils()

from pyspark.sql import SparkSession
from pyspark.sql.types import StructType, StructField, StringType, IntegerType, DoubleType, TimestampType

# Import framework
from src.data_testing_framework import DataTestingFramework
from src.utils.logging_config import configure_logging


def get_workflow_parameters() -> Dict[str, Any]:
    """Get parameters from Databricks workflow or use defaults"""
    
    return {
        "table_path": dbutils.widgets.get("table_path", ""),
        "expected_count": int(dbutils.widgets.get("expected_count", "0")) or None,
        "batch_id": dbutils.widgets.get("batch_id", f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}"),
        "source_system": dbutils.widgets.get("source_system", "unknown"),
        "data_date": dbutils.widgets.get("data_date", datetime.now().strftime("%Y-%m-%d")),
        "notification_level": dbutils.widgets.get("notification_level", "critical"),  # critical, warning, info, none
        "fail_on_warning": dbutils.widgets.get("fail_on_warning", "false").lower() == "true",
        "quarantine_enabled": dbutils.widgets.get("quarantine_enabled", "true").lower() == "true"
    }


def create_databricks_config(params: Dict[str, Any]) -> Dict[str, Any]:
    """Create configuration for Databricks environment"""
    
    return {
        "environment": {
            "name": "production" if DATABRICKS_ENV else "test",
            "catalog_name": "main",
            "schema_name": "bronze"
        },
        "bronze_layer": {
            "tests": {
                "file_integrity": True,
                "schema_validation": True,
                "record_counting": True,
                "timestamp_validation": True,
                "metadata_preservation": True,
                "duplicate_detection": True
            },
            "thresholds": {
                "completeness_min": 0.95,
                "accuracy_min": 0.98,
                "consistency_min": 0.95,
                "validity_min": 0.98,
                "timeliness_hours": 24,
                "uniqueness_min": 0.98,
                "duplicate_threshold": 0.05
            }
        },
        "quality_gates": {
            "pass_threshold": 0.95,
            "warning_threshold": 0.85,
            "quarantine": {
                "enabled": params["quarantine_enabled"],
                "storage_path": "/mnt/quarantine/bronze",
                "retention_days": 30
            },
            "escalation": {
                "emergency_contact_threshold": 0.5,
                "auto_recovery_enabled": False
            }
        },
        "notifications": {
            "email": {
                "enabled": params["notification_level"] != "none",
                "smtp_server": "smtp.company.com",
                "smtp_port": 587,
                "smtp_username": dbutils.secrets.get("email", "username") if DATABRICKS_ENV else "",
                "smtp_password": dbutils.secrets.get("email", "password") if DATABRICKS_ENV else "",
                "from_address": "<EMAIL>",
                "recipients": {
                    "critical": ["<EMAIL>", "<EMAIL>"],
                    "warning": ["<EMAIL>"],
                    "info": ["<EMAIL>"]
                }
            },
            "slack": {
                "enabled": False  # Disabled as requested
            }
        },
        "logging": {
            "level": "INFO",
            "destinations": {
                "console": True,
                "databricks": DATABRICKS_ENV
            },
            "databricks_logging": {
                "log_table": "main.logs.data_quality_logs"
            }
        }
    }


def validate_parameters(params: Dict[str, Any]) -> bool:
    """Validate workflow parameters"""
    
    if not params["table_path"]:
        print("❌ ERROR: table_path parameter is required")
        return False
    
    if not params["source_system"]:
        print("⚠️  WARNING: source_system not specified, using 'unknown'")
    
    print("✅ Parameters validated successfully")
    return True


def log_execution_start(params: Dict[str, Any]) -> None:
    """Log execution start details"""
    
    print("🚀 BRONZE LAYER DATA QUALITY CHECK STARTED")
    print("=" * 60)
    print(f"📊 Table Path: {params['table_path']}")
    print(f"🔢 Expected Count: {params['expected_count'] or 'Not specified'}")
    print(f"📦 Batch ID: {params['batch_id']}")
    print(f"🏢 Source System: {params['source_system']}")
    print(f"📅 Data Date: {params['data_date']}")
    print(f"📧 Notification Level: {params['notification_level']}")
    print(f"⚠️  Fail on Warning: {params['fail_on_warning']}")
    print(f"🔒 Quarantine Enabled: {params['quarantine_enabled']}")
    print("=" * 60)


def determine_workflow_action(results: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
    """Determine what action the workflow should take based on results"""
    
    decision = results['quality_gate']['decision']
    overall_score = results['quality_gate']['overall_score']
    
    # Workflow decision logic
    if decision == "PASS":
        return {
            "status": "success",
            "action": "proceed",
            "message": "✅ Data quality check PASSED - proceed to Silver layer",
            "exit_code": 0,
            "continue_pipeline": True
        }
    
    elif decision == "WARNING":
        if params["fail_on_warning"]:
            return {
                "status": "warning_blocked",
                "action": "stop",
                "message": "⚠️ Data quality WARNING detected - pipeline blocked due to fail_on_warning=true",
                "exit_code": 1,
                "continue_pipeline": False
            }
        else:
            return {
                "status": "warning_proceed",
                "action": "proceed_with_caution",
                "message": "⚠️ Data quality WARNING detected - proceeding with caution",
                "exit_code": 0,
                "continue_pipeline": True
            }
    
    elif decision in ["FAIL", "QUARANTINE"]:
        return {
            "status": "failed",
            "action": "stop",
            "message": f"❌ Data quality check FAILED ({decision}) - pipeline blocked",
            "exit_code": 1,
            "continue_pipeline": False
        }
    
    elif decision == "EMERGENCY":
        return {
            "status": "emergency",
            "action": "halt_all",
            "message": "🚨 EMERGENCY: Critical data quality issues detected - all processing halted",
            "exit_code": 2,
            "continue_pipeline": False
        }
    
    else:
        return {
            "status": "unknown",
            "action": "stop",
            "message": f"❓ Unknown quality gate decision: {decision}",
            "exit_code": 1,
            "continue_pipeline": False
        }


def save_results_to_delta(results: Dict[str, Any], params: Dict[str, Any], spark: SparkSession) -> None:
    """Save results to Delta table for historical tracking"""
    
    try:
        # Prepare results data
        results_data = {
            "execution_id": results['execution_id'],
            "execution_timestamp": datetime.now(),
            "table_path": params['table_path'],
            "batch_id": params['batch_id'],
            "source_system": params['source_system'],
            "data_date": params['data_date'],
            "quality_decision": results['quality_gate']['decision'],
            "overall_score": results['quality_gate']['overall_score'],
            "threshold_met": results['quality_gate']['threshold_met'],
            "total_tests": results['test_summary']['total_tests'],
            "passed_tests": results['test_summary']['passed'],
            "warning_tests": results['test_summary']['warnings'],
            "failed_tests": results['test_summary']['failed'],
            "execution_time_seconds": results['execution_time_seconds'],
            "quarantine_required": results['quality_gate']['quarantine_required'],
            "emergency_escalation": results['quality_gate']['emergency_escalation'],
            "framework_version": results['framework_metadata']['version'],
            "environment": "production" if DATABRICKS_ENV else "test"
        }
        
        # Create DataFrame
        results_df = spark.createDataFrame([results_data])
        
        # Save to Delta table
        results_table = "main.logs.bronze_quality_results"
        results_df.write.mode("append").option("mergeSchema", "true").saveAsTable(results_table)
        
        print(f"✅ Results saved to {results_table}")
        
    except Exception as e:
        print(f"⚠️ Failed to save results to Delta table: {e}")
        # Don't fail the workflow for logging issues


def main():
    """Main workflow function"""
    
    try:
        # Get workflow parameters
        params = get_workflow_parameters()
        
        # Validate parameters
        if not validate_parameters(params):
            dbutils.notebook.exit({
                "status": "error",
                "message": "Invalid parameters",
                "exit_code": 1
            })
        
        # Log execution start
        log_execution_start(params)
        
        # Configure logging
        configure_logging({
            "level": "INFO",
            "destinations": {"console": True, "databricks": DATABRICKS_ENV}
        })
        
        # Get Spark session
        spark = SparkSession.getActiveSession()
        if not spark:
            spark = SparkSession.builder.appName("BronzeDataQualityCheck").getOrCreate()
        
        # Create configuration
        config = create_databricks_config(params)
        
        # Initialize framework
        print("🔧 Initializing Data Testing Framework...")
        framework = DataTestingFramework(config_dict=config, spark=spark)
        
        # Validate framework configuration
        validation = framework.validate_configuration()
        if not validation["config_valid"]:
            print("❌ Framework configuration validation failed:")
            for issue in validation["issues"]:
                print(f"   • {issue}")
            
            dbutils.notebook.exit({
                "status": "config_error",
                "message": "Framework configuration invalid",
                "exit_code": 1
            })
        
        # Prepare source metadata
        source_metadata = {
            "source_system": params["source_system"],
            "batch_id": params["batch_id"],
            "data_date": params["data_date"],
            "extraction_timestamp": datetime.now().isoformat(),
            "workflow_run_id": dbutils.notebook.entry_point.getDbutils().notebook().getContext().currentRunId().get() if DATABRICKS_ENV else "local_run"
        }
        
        # Run data quality tests
        print("🔍 Running Bronze layer data quality tests...")
        results = framework.run_bronze_pipeline_tests(
            table_path=params["table_path"],
            expected_count=params["expected_count"],
            source_metadata=source_metadata,
            timestamp_column="ingestion_timestamp",
            send_notifications=(params["notification_level"] != "none")
        )
        
        # Save results to Delta table
        save_results_to_delta(results, params, spark)
        
        # Determine workflow action
        workflow_action = determine_workflow_action(results, params)
        
        # Log final results
        print("\n" + "=" * 60)
        print("📊 FINAL RESULTS")
        print("=" * 60)
        print(f"🎯 Quality Decision: {results['quality_gate']['decision']}")
        print(f"📈 Overall Score: {results['quality_gate']['overall_score']:.2%}")
        print(f"🚦 Workflow Action: {workflow_action['action']}")
        print(f"💬 Message: {workflow_action['message']}")
        print("=" * 60)
        
        # Exit with appropriate status
        exit_data = {
            "status": workflow_action["status"],
            "action": workflow_action["action"],
            "message": workflow_action["message"],
            "continue_pipeline": workflow_action["continue_pipeline"],
            "quality_decision": results['quality_gate']['decision'],
            "overall_score": results['quality_gate']['overall_score'],
            "execution_id": results['execution_id'],
            "exit_code": workflow_action["exit_code"]
        }
        
        dbutils.notebook.exit(exit_data)
        
    except Exception as e:
        error_message = f"❌ Data quality check failed with error: {str(e)}"
        print(error_message)
        
        # Log error details
        import traceback
        traceback.print_exc()
        
        # Exit with error status
        dbutils.notebook.exit({
            "status": "error",
            "message": error_message,
            "exit_code": 1
        })


if __name__ == "__main__":
    main()
