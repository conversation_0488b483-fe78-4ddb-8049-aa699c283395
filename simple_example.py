#!/usr/bin/env python3
"""
Simple DataFrame Validation Example

This script shows how to validate any DataFrame against entity-specific schemas.
The validation function automatically picks the right JSON schema file based on entity name.
"""

from pyspark.sql import SparkSession
from pyspark.sql.types import StructType, StructField, StringType, IntegerType, TimestampType
from datetime import datetime
from data_quality_check import validate_dataframe


def create_sample_data(spark):
    """Create sample DataFrames for testing"""
    
    # Orders DataFrame
    orders_schema = StructType([
        StructField("order_id", StringType(), False),
        StructField("customer_id", StringType(), False),
        StructField("product_id", StringType(), False),
        StructField("quantity", IntegerType(), False),
        StructField("price", StringType(), False),  # Wrong type - should be double
        Struct<PERSON>ield("order_date", TimestampType(), False),
        <PERSON>ruct<PERSON><PERSON>("ingestion_timestamp", TimestampType(), False)
    ])
    
    orders_data = [
        ("ORD001", "CUST001", "PROD001", 2, "29.99", datetime.now(), datetime.now()),
        ("ORD002", "CUST002", "PROD002", 1, "15.50", datetime.now(), datetime.now()),
        ("ORD003", "CUST001", "PROD003", 3, "45.00", datetime.now(), datetime.now())
    ]
    
    orders_df = spark.createDataFrame(orders_data, orders_schema)
    
    # Customers DataFrame
    customers_schema = StructType([
        StructField("customer_id", StringType(), False),
        StructField("first_name", StringType(), False),
        StructField("last_name", StringType(), False),
        StructField("email", StringType(), False),
        StructField("phone", StringType(), True),
        StructField("registration_date", TimestampType(), False),
        StructField("ingestion_timestamp", TimestampType(), False)
    ])
    
    customers_data = [
        ("CUST001", "John", "Doe", "<EMAIL>", "555-1234", datetime.now(), datetime.now()),
        ("CUST002", "Jane", "Smith", "<EMAIL>", None, datetime.now(), datetime.now())
    ]
    
    customers_df = spark.createDataFrame(customers_data, customers_schema)
    
    return orders_df, customers_df


def main():
    """Main validation example"""
    
    # Initialize Spark
    spark = SparkSession.builder \
        .appName("DataFrameValidation") \
        .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer") \
        .getOrCreate()
    
    print("🚀 DATAFRAME VALIDATION EXAMPLES")
    print("=" * 50)
    
    # Create sample data
    orders_df, customers_df = create_sample_data(spark)
    
    # Example 1: Validate Orders DataFrame
    print("\n📦 EXAMPLE 1: Validating Orders DataFrame")
    print("-" * 40)
    
    orders_result = validate_dataframe(
        df=orders_df,
        entity_name="orders",
        expected_count=3
    )
    
    print(f"✅ Entity: {orders_result['entity']}")
    print(f"✅ Decision: {orders_result['decision']}")
    print(f"✅ Message: {orders_result['message']}")
    
    # Example 2: Validate Customers DataFrame
    print("\n👥 EXAMPLE 2: Validating Customers DataFrame")
    print("-" * 40)
    
    customers_result = validate_dataframe(
        df=customers_df,
        entity_name="customers",
        expected_count=2
    )
    
    print(f"✅ Entity: {customers_result['entity']}")
    print(f"✅ Decision: {customers_result['decision']}")
    print(f"✅ Message: {customers_result['message']}")
    
    # Example 3: Entity without schema file
    print("\n🔍 EXAMPLE 3: Entity without schema file")
    print("-" * 40)
    
    unknown_result = validate_dataframe(
        df=orders_df,
        entity_name="products",  # No products_schema.json exists
        expected_count=3
    )
    
    print(f"✅ Entity: {unknown_result['entity']}")
    print(f"✅ Decision: {unknown_result['decision']}")
    print(f"✅ Message: {unknown_result['message']}")
    
    spark.stop()


if __name__ == "__main__":
    main()
