"""
Quality Gate Engine

This module implements the decision-making system for Pass/Warning/Fail logic
with configurable thresholds and quarantine capabilities.
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Dict, Any, List, Optional
from enum import Enum
import structlog

from ..bronze_layer.test_results import BronzeTestSuite, TestStatus
from ..config.config_manager import DataTestingConfig

logger = structlog.get_logger(__name__)


class QualityGateDecision(Enum):
    """Quality gate decision outcomes"""
    PASS = "PASS"
    WARNING = "WARNING"
    FAIL = "FAIL"
    QUARANTINE = "QUARANTINE"
    EMERGENCY = "EMERGENCY"


@dataclass
class QualityGateResult:
    """Result of quality gate evaluation"""
    decision: QualityGateDecision
    overall_score: float
    threshold_met: bool
    reasons: List[str]
    recommendations: List[str]
    quarantine_required: bool = False
    emergency_escalation: bool = False
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class QualityGateEngine:
    """Quality gate decision engine"""
    
    def __init__(self, config: DataTestingConfig):
        """
        Initialize Quality Gate Engine
        
        Args:
            config: Data testing configuration
        """
        self.config = config
        self.gate_config = config.quality_gates
        self.quarantine_config = self.gate_config.quarantine
        self.escalation_config = self.gate_config.escalation
        
        logger.info("Quality Gate Engine initialized",
                   pass_threshold=self.gate_config.pass_threshold,
                   warning_threshold=self.gate_config.warning_threshold)
    
    def evaluate(self, test_suite: BronzeTestSuite) -> QualityGateResult:
        """
        Evaluate test results and make quality gate decision
        
        Args:
            test_suite: Completed Bronze layer test suite
            
        Returns:
            QualityGateResult with decision and recommendations
        """
        logger.info("Evaluating quality gate", 
                   execution_id=test_suite.execution_id,
                   overall_status=test_suite.overall_status.value)
        
        overall_score = test_suite.get_overall_score()
        reasons = []
        recommendations = []
        
        # Determine base decision based on score and thresholds
        if overall_score >= self.gate_config.pass_threshold:
            base_decision = QualityGateDecision.PASS
            reasons.append(f"Overall score {overall_score:.2%} meets pass threshold {self.gate_config.pass_threshold:.2%}")
        elif overall_score >= self.gate_config.warning_threshold:
            base_decision = QualityGateDecision.WARNING
            reasons.append(f"Overall score {overall_score:.2%} meets warning threshold {self.gate_config.warning_threshold:.2%}")
            recommendations.append("Monitor data quality trends and consider improvements")
        else:
            base_decision = QualityGateDecision.FAIL
            reasons.append(f"Overall score {overall_score:.2%} below warning threshold {self.gate_config.warning_threshold:.2%}")
            recommendations.append("Data quality issues must be addressed before proceeding")
        
        # Check for critical failures that override score-based decisions
        critical_failures = self._check_critical_failures(test_suite)
        if critical_failures:
            base_decision = QualityGateDecision.FAIL
            reasons.extend(critical_failures)
            recommendations.append("Address critical data quality failures immediately")
        
        # Check for emergency conditions
        emergency_escalation = self._check_emergency_conditions(test_suite, overall_score)
        if emergency_escalation:
            base_decision = QualityGateDecision.EMERGENCY
            reasons.append("Emergency conditions detected - immediate attention required")
            recommendations.append("Escalate to emergency response team")
        
        # Determine quarantine requirements
        quarantine_required = self._should_quarantine(test_suite, base_decision)
        
        # Final decision logic
        final_decision = self._make_final_decision(base_decision, quarantine_required)
        
        # Generate additional recommendations
        recommendations.extend(self._generate_recommendations(test_suite, final_decision))
        
        result = QualityGateResult(
            decision=final_decision,
            overall_score=overall_score,
            threshold_met=overall_score >= self.gate_config.warning_threshold,
            reasons=reasons,
            recommendations=recommendations,
            quarantine_required=quarantine_required,
            emergency_escalation=emergency_escalation,
            metadata={
                "evaluation_time": datetime.now().isoformat(),
                "pass_threshold": self.gate_config.pass_threshold,
                "warning_threshold": self.gate_config.warning_threshold,
                "test_summary": test_suite.get_summary()
            }
        )
        
        logger.info("Quality gate evaluation completed",
                   decision=final_decision.value,
                   overall_score=overall_score,
                   quarantine_required=quarantine_required)
        
        return result
    
    def _check_critical_failures(self, test_suite: BronzeTestSuite) -> List[str]:
        """Check for critical failures that require immediate attention"""
        critical_failures = []
        
        # Check for ERROR status tests
        error_tests = [r for r in test_suite.test_results if r.status == TestStatus.ERROR]
        if error_tests:
            critical_failures.append(f"{len(error_tests)} tests failed with errors")
        
        # Check for file integrity failures
        file_integrity_tests = [r for r in test_suite.test_results 
                               if r.test_name == "file_integrity" and r.status == TestStatus.FAIL]
        if file_integrity_tests:
            critical_failures.append("File integrity check failed - data may be corrupted")
        
        # Check for zero record count
        for result in test_suite.test_results:
            if result.test_name == "record_counting":
                for metric in result.metrics:
                    if metric.name == "total_records" and metric.value == 0:
                        critical_failures.append("No records found in dataset")
        
        return critical_failures
    
    def _check_emergency_conditions(self, test_suite: BronzeTestSuite, overall_score: float) -> bool:
        """Check for conditions that require emergency escalation"""
        emergency_threshold = self.escalation_config.emergency_contact_threshold
        
        # Score-based emergency
        if overall_score < emergency_threshold:
            return True
        
        # Multiple critical system failures
        critical_test_failures = len([r for r in test_suite.test_results 
                                    if r.status in [TestStatus.ERROR, TestStatus.FAIL]])
        if critical_test_failures >= 3:
            return True
        
        # Data corruption indicators
        for result in test_suite.test_results:
            if result.test_name == "file_integrity" and result.status == TestStatus.ERROR:
                return True
        
        return False
    
    def _should_quarantine(self, test_suite: BronzeTestSuite, decision: QualityGateDecision) -> bool:
        """Determine if data should be quarantined"""
        if not self.quarantine_config.enabled:
            return False
        
        # Always quarantine on FAIL or EMERGENCY
        if decision in [QualityGateDecision.FAIL, QualityGateDecision.EMERGENCY]:
            return True
        
        # Quarantine on specific test failures
        quarantine_triggers = [
            "file_integrity",
            "schema_validation"
        ]
        
        for result in test_suite.test_results:
            if (result.test_name in quarantine_triggers and 
                result.status in [TestStatus.FAIL, TestStatus.ERROR]):
                return True
        
        return False
    
    def _make_final_decision(self, base_decision: QualityGateDecision, quarantine_required: bool) -> QualityGateDecision:
        """Make final decision considering all factors"""
        if base_decision == QualityGateDecision.EMERGENCY:
            return QualityGateDecision.EMERGENCY
        
        if quarantine_required:
            return QualityGateDecision.QUARANTINE
        
        return base_decision
    
    def _generate_recommendations(self, test_suite: BronzeTestSuite, decision: QualityGateDecision) -> List[str]:
        """Generate specific recommendations based on test results and decision"""
        recommendations = []
        
        if decision == QualityGateDecision.PASS:
            recommendations.append("Data quality meets standards - proceed to Silver layer processing")
            
            # Still provide improvement suggestions for near-threshold scores
            if test_suite.get_overall_score() < 0.98:
                recommendations.append("Consider minor optimizations to achieve excellent quality scores")
        
        elif decision == QualityGateDecision.WARNING:
            recommendations.append("Data quality acceptable but monitor closely")
            recommendations.append("Schedule quality improvement review within 1 week")
            
            # Specific recommendations for warning tests
            warning_tests = test_suite.get_warning_tests()
            for test in warning_tests:
                if test.test_name == "timestamp_validation":
                    recommendations.append("Review data ingestion timing and scheduling")
                elif test.test_name == "schema_validation":
                    recommendations.append("Coordinate with source systems on schema changes")
        
        elif decision in [QualityGateDecision.FAIL, QualityGateDecision.QUARANTINE]:
            recommendations.append("Do not proceed to Silver layer until issues are resolved")
            recommendations.append("Investigate root causes with source system teams")
            
            # Specific recommendations for failed tests
            failed_tests = test_suite.get_failed_tests()
            for test in failed_tests:
                if test.test_name == "file_integrity":
                    recommendations.append("Check file transfer processes and network connectivity")
                elif test.test_name == "record_counting":
                    recommendations.append("Verify source system data extraction completeness")
                elif test.test_name == "schema_validation":
                    recommendations.append("Update schema expectations or fix source data structure")
        
        elif decision == QualityGateDecision.EMERGENCY:
            recommendations.append("IMMEDIATE ACTION REQUIRED - Contact emergency response team")
            recommendations.append("Halt all downstream processing until investigation complete")
            recommendations.append("Preserve current data state for forensic analysis")
        
        return recommendations
    
    def get_decision_summary(self, result: QualityGateResult) -> Dict[str, Any]:
        """Get a summary of the quality gate decision"""
        return {
            "decision": result.decision.value,
            "overall_score": result.overall_score,
            "threshold_met": result.threshold_met,
            "quarantine_required": result.quarantine_required,
            "emergency_escalation": result.emergency_escalation,
            "reason_count": len(result.reasons),
            "recommendation_count": len(result.recommendations),
            "evaluation_time": result.metadata.get("evaluation_time"),
            "next_actions": self._get_next_actions(result.decision)
        }
    
    def _get_next_actions(self, decision: QualityGateDecision) -> List[str]:
        """Get next actions based on decision"""
        actions = {
            QualityGateDecision.PASS: [
                "Proceed to Silver layer processing",
                "Log successful quality validation",
                "Continue monitoring"
            ],
            QualityGateDecision.WARNING: [
                "Proceed with caution to Silver layer",
                "Increase monitoring frequency",
                "Schedule quality review"
            ],
            QualityGateDecision.FAIL: [
                "Stop Silver layer processing",
                "Investigate quality issues",
                "Fix issues and retest"
            ],
            QualityGateDecision.QUARANTINE: [
                "Move data to quarantine storage",
                "Notify data quality team",
                "Begin remediation process"
            ],
            QualityGateDecision.EMERGENCY: [
                "Escalate to emergency team",
                "Halt all processing",
                "Begin incident response"
            ]
        }
        
        return actions.get(decision, ["Review decision and take appropriate action"])
