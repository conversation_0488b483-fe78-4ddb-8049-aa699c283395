"""
Logging Configuration

This module sets up structured logging for the data testing framework
with support for console, file, and Databricks logging.
"""

import os
import sys
import logging
import logging.handlers
from pathlib import Path
from typing import Dict, Any, Optional
import structlog
from datetime import datetime


def configure_logging(config: Optional[Dict[str, Any]] = None) -> None:
    """
    Configure structured logging for the framework
    
    Args:
        config: Logging configuration dictionary
    """
    if config is None:
        config = {
            "level": "INFO",
            "format": "json",
            "destinations": {
                "console": True,
                "file": False,
                "databricks": False
            }
        }
    
    # Set log level
    log_level = getattr(logging, config.get("level", "INFO").upper())
    
    # Configure structlog
    processors = [
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
    ]
    
    # Add appropriate renderer based on format
    if config.get("format", "json").lower() == "json":
        processors.append(structlog.processors.JSONRenderer())
    else:
        processors.append(structlog.dev.ConsoleRenderer())
    
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=True,
    )
    
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=log_level,
    )
    
    # Set up handlers based on destinations
    root_logger = logging.getLogger()
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    destinations = config.get("destinations", {})
    
    # Console logging
    if destinations.get("console", True):
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(log_level)
        root_logger.addHandler(console_handler)
    
    # File logging
    if destinations.get("file", False):
        file_config = config.get("file_logging", {})
        log_path = Path(file_config.get("path", "/tmp/data_quality_logs"))
        log_path.mkdir(parents=True, exist_ok=True)
        
        log_file = log_path / f"data_quality_{datetime.now().strftime('%Y%m%d')}.log"
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=file_config.get("max_size_mb", 100) * 1024 * 1024,
            backupCount=file_config.get("backup_count", 5)
        )
        file_handler.setLevel(log_level)
        root_logger.addHandler(file_handler)
    
    # Databricks logging (if running in Databricks)
    if destinations.get("databricks", False) and _is_databricks_environment():
        try:
            databricks_handler = DatabricksLogHandler(config.get("databricks_logging", {}))
            databricks_handler.setLevel(log_level)
            root_logger.addHandler(databricks_handler)
        except Exception as e:
            print(f"Failed to configure Databricks logging: {e}")


def _is_databricks_environment() -> bool:
    """Check if running in Databricks environment"""
    return (
        "DATABRICKS_RUNTIME_VERSION" in os.environ or
        "SPARK_HOME" in os.environ and "databricks" in os.environ.get("SPARK_HOME", "").lower()
    )


class DatabricksLogHandler(logging.Handler):
    """Custom log handler for Databricks environment"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.log_table = config.get("log_table", "data_quality_logs")
        self.batch_size = config.get("batch_size", 1000)
        self.log_buffer = []
        
        # Initialize Spark session for logging
        try:
            from pyspark.sql import SparkSession
            self.spark = SparkSession.getActiveSession()
            if self.spark is None:
                self.spark = SparkSession.builder.appName("DataQualityLogging").getOrCreate()
        except ImportError:
            self.spark = None
    
    def emit(self, record: logging.LogRecord) -> None:
        """Emit a log record to Databricks table"""
        if self.spark is None:
            return
        
        try:
            log_entry = {
                "timestamp": datetime.fromtimestamp(record.created).isoformat(),
                "level": record.levelname,
                "logger": record.name,
                "message": self.format(record),
                "module": record.module,
                "function": record.funcName,
                "line": record.lineno,
                "thread": record.thread,
                "process": record.process
            }
            
            # Add exception info if present
            if record.exc_info:
                log_entry["exception"] = self.formatException(record.exc_info)
            
            self.log_buffer.append(log_entry)
            
            # Flush buffer if it reaches batch size
            if len(self.log_buffer) >= self.batch_size:
                self.flush()
                
        except Exception:
            # Don't let logging errors break the application
            pass
    
    def flush(self) -> None:
        """Flush log buffer to Databricks table"""
        if not self.log_buffer or self.spark is None:
            return
        
        try:
            df = self.spark.createDataFrame(self.log_buffer)
            df.write.mode("append").saveAsTable(self.log_table)
            self.log_buffer.clear()
        except Exception:
            # Don't let logging errors break the application
            pass


class MetricsCollector:
    """Collects and tracks metrics for the data testing framework"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize metrics collector
        
        Args:
            config: Metrics configuration
        """
        self.config = config or {}
        self.metrics = {}
        self.enabled = self.config.get("enabled", True)
        
        if self.enabled:
            self._setup_prometheus_metrics()
    
    def _setup_prometheus_metrics(self) -> None:
        """Setup Prometheus metrics if enabled"""
        prometheus_config = self.config.get("prometheus", {})
        if not prometheus_config.get("enabled", False):
            return
        
        try:
            from prometheus_client import Counter, Histogram, Gauge, start_http_server
            
            prefix = prometheus_config.get("metrics_prefix", "data_quality")
            
            # Define metrics
            self.test_counter = Counter(
                f"{prefix}_tests_total",
                "Total number of data quality tests run",
                ["test_type", "status", "table"]
            )
            
            self.test_duration = Histogram(
                f"{prefix}_test_duration_seconds",
                "Duration of data quality tests",
                ["test_type", "table"]
            )
            
            self.quality_score = Gauge(
                f"{prefix}_quality_score",
                "Overall quality score",
                ["table"]
            )
            
            self.quarantine_count = Counter(
                f"{prefix}_quarantine_total",
                "Number of records quarantined",
                ["table", "reason"]
            )
            
            # Start metrics server
            port = prometheus_config.get("port", 8000)
            start_http_server(port)
            
        except ImportError:
            print("Prometheus client not available, metrics collection disabled")
    
    def record_test_execution(self, test_name: str, status: str, duration: float, table: str) -> None:
        """Record test execution metrics"""
        if not self.enabled:
            return
        
        try:
            if hasattr(self, 'test_counter'):
                self.test_counter.labels(test_type=test_name, status=status, table=table).inc()
            
            if hasattr(self, 'test_duration'):
                self.test_duration.labels(test_type=test_name, table=table).observe(duration)
                
        except Exception:
            pass  # Don't let metrics collection break the application
    
    def record_quality_score(self, table: str, score: float) -> None:
        """Record quality score metric"""
        if not self.enabled:
            return
        
        try:
            if hasattr(self, 'quality_score'):
                self.quality_score.labels(table=table).set(score)
        except Exception:
            pass
    
    def record_quarantine(self, table: str, count: int, reason: str) -> None:
        """Record quarantine metrics"""
        if not self.enabled:
            return
        
        try:
            if hasattr(self, 'quarantine_count'):
                self.quarantine_count.labels(table=table, reason=reason).inc(count)
        except Exception:
            pass


# Global metrics collector instance
_metrics_collector: Optional[MetricsCollector] = None


def get_metrics_collector() -> Optional[MetricsCollector]:
    """Get the global metrics collector instance"""
    return _metrics_collector


def initialize_metrics(config: Optional[Dict[str, Any]] = None) -> None:
    """Initialize the global metrics collector"""
    global _metrics_collector
    _metrics_collector = MetricsCollector(config)
