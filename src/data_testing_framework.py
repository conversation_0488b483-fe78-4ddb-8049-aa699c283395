"""
Medallion Architecture Data Testing Framework

Main orchestrator class that coordinates Bronze layer testing, quality gates,
and notifications for the medallion architecture data pipeline.
"""

import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
from pyspark.sql import SparkSession
from pyspark.sql.types import StructType
import structlog

from .config.config_manager import ConfigManager, DataTestingConfig
from .bronze_layer.bronze_tester import BronzeTester
from .quality_gates.quality_gate import QualityGateEngine, QualityGateResult
from .notifications.notification_manager import NotificationManager
from .bronze_layer.test_results import BronzeTestSuite

logger = structlog.get_logger(__name__)


class DataTestingFramework:
    """Main orchestrator for the data testing framework"""
    
    def __init__(self, config_path: Optional[str] = None, spark: Optional[SparkSession] = None):
        """
        Initialize the Data Testing Framework
        
        Args:
            config_path: Path to configuration file
            spark: SparkSession instance (will create if not provided)
        """
        # Load configuration
        self.config_manager = ConfigManager(config_path)
        self.config = self.config_manager.get_config()
        
        # Initialize components
        self.bronze_tester = BronzeTester(config_path, spark)
        self.quality_gate_engine = QualityGateEngine(self.config)
        self.notification_manager = NotificationManager(self.config)
        
        logger.info("Data Testing Framework initialized",
                   environment=self.config.environment.name,
                   catalog=self.config.environment.catalog_name,
                   schema=self.config.environment.schema_name)
    
    def run_bronze_pipeline_tests(self, 
                                 table_path: str,
                                 expected_schema: Optional[StructType] = None,
                                 expected_count: Optional[int] = None,
                                 source_metadata: Optional[Dict[str, Any]] = None,
                                 timestamp_column: str = "ingestion_timestamp",
                                 send_notifications: bool = True) -> Dict[str, Any]:
        """
        Run complete Bronze layer pipeline testing workflow
        
        This is the main entry point that should be called after each pipeline run.
        
        Args:
            table_path: Path to the Bronze layer table to test
            expected_schema: Expected schema for validation
            expected_count: Expected number of records
            source_metadata: Source system metadata
            timestamp_column: Name of timestamp column for validation
            send_notifications: Whether to send notifications
            
        Returns:
            Dictionary with complete test results and decisions
        """
        execution_id = str(uuid.uuid4())
        start_time = datetime.now()
        
        logger.info("Starting Bronze pipeline tests",
                   table_path=table_path,
                   execution_id=execution_id)
        
        try:
            # Step 1: Run Bronze layer tests
            logger.info("Running Bronze layer tests")
            test_suite = self.bronze_tester.run_tests(
                table_path=table_path,
                expected_schema=expected_schema,
                expected_count=expected_count,
                source_metadata=source_metadata,
                timestamp_column=timestamp_column
            )
            
            # Step 2: Evaluate quality gates
            logger.info("Evaluating quality gates")
            quality_gate_result = self.quality_gate_engine.evaluate(test_suite)
            
            # Step 3: Send notifications if enabled
            notification_results = {}
            if send_notifications:
                logger.info("Sending notifications")
                notification_results = self.notification_manager.send_notifications(
                    test_suite, quality_gate_result
                )
            
            # Step 4: Compile final results
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            final_results = {
                "execution_id": execution_id,
                "table_path": table_path,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "execution_time_seconds": execution_time,
                
                # Test results
                "test_suite": test_suite.to_dict(),
                "test_summary": test_suite.get_summary(),
                
                # Quality gate results
                "quality_gate": {
                    "decision": quality_gate_result.decision.value,
                    "overall_score": quality_gate_result.overall_score,
                    "threshold_met": quality_gate_result.threshold_met,
                    "quarantine_required": quality_gate_result.quarantine_required,
                    "emergency_escalation": quality_gate_result.emergency_escalation,
                    "reasons": quality_gate_result.reasons,
                    "recommendations": quality_gate_result.recommendations
                },
                
                # Notification results
                "notifications": notification_results,
                
                # Next actions
                "next_actions": self._determine_next_actions(quality_gate_result),
                
                # Framework metadata
                "framework_metadata": {
                    "version": "1.0.0",
                    "environment": self.config.environment.name,
                    "config_path": str(self.config_manager.config_path)
                }
            }
            
            logger.info("Bronze pipeline tests completed",
                       execution_id=execution_id,
                       decision=quality_gate_result.decision.value,
                       overall_score=quality_gate_result.overall_score,
                       execution_time=execution_time)
            
            return final_results
            
        except Exception as e:
            logger.error("Bronze pipeline tests failed",
                        execution_id=execution_id,
                        error=str(e))
            
            # Return error result
            return {
                "execution_id": execution_id,
                "table_path": table_path,
                "start_time": start_time.isoformat(),
                "end_time": datetime.now().isoformat(),
                "status": "ERROR",
                "error": str(e),
                "next_actions": ["Investigate framework error", "Check logs", "Retry after fixing issues"]
            }
    
    def _determine_next_actions(self, quality_gate_result: QualityGateResult) -> List[str]:
        """Determine next actions based on quality gate decision"""
        return self.quality_gate_engine._get_next_actions(quality_gate_result.decision)
    
    def validate_configuration(self) -> Dict[str, Any]:
        """
        Validate the current configuration
        
        Returns:
            Dictionary with validation results
        """
        logger.info("Validating configuration")
        
        validation_results = {
            "config_valid": True,
            "issues": [],
            "warnings": [],
            "recommendations": []
        }
        
        try:
            # Validate configuration loading
            config_valid = self.config_manager.validate_config()
            if not config_valid:
                validation_results["config_valid"] = False
                validation_results["issues"].append("Configuration validation failed")
            
            # Check Spark connectivity
            try:
                spark_version = self.bronze_tester.spark.version
                validation_results["spark_version"] = spark_version
            except Exception as e:
                validation_results["config_valid"] = False
                validation_results["issues"].append(f"Spark connectivity issue: {str(e)}")
            
            # Check notification configurations
            if self.config.notifications.email.enabled:
                if not self.config.notifications.email.smtp_server:
                    validation_results["warnings"].append("Email enabled but SMTP server not configured")
                if not self.config.notifications.email.recipients.get("critical"):
                    validation_results["warnings"].append("No critical email recipients configured")
            
            if self.config.notifications.slack.enabled:
                if not self.config.notifications.slack.webhook_url:
                    validation_results["issues"].append("Slack enabled but webhook URL not configured")
            
            # Check thresholds
            bronze_thresholds = self.config.bronze_layer.thresholds
            if bronze_thresholds.completeness_min < 0.8:
                validation_results["warnings"].append("Completeness threshold is quite low (< 80%)")
            
            if self.config.quality_gates.pass_threshold <= self.config.quality_gates.warning_threshold:
                validation_results["issues"].append("Pass threshold must be higher than warning threshold")
            
            # Generate recommendations
            if not validation_results["issues"]:
                validation_results["recommendations"].append("Configuration looks good!")
            
            if validation_results["warnings"]:
                validation_results["recommendations"].append("Consider addressing configuration warnings")
            
            logger.info("Configuration validation completed",
                       valid=validation_results["config_valid"],
                       issues=len(validation_results["issues"]),
                       warnings=len(validation_results["warnings"]))
            
        except Exception as e:
            validation_results["config_valid"] = False
            validation_results["issues"].append(f"Validation error: {str(e)}")
            logger.error("Configuration validation failed", error=str(e))
        
        return validation_results
    
    def test_notifications(self, channel: str = "all") -> Dict[str, Any]:
        """
        Test notification configuration by sending test messages
        
        Args:
            channel: Which channel to test ("email", "slack", "webhooks", or "all")
            
        Returns:
            Dictionary with test results
        """
        logger.info("Testing notifications", channel=channel)
        
        try:
            success = self.notification_manager.send_test_notification(channel)
            
            return {
                "success": success,
                "channel": channel,
                "timestamp": datetime.now().isoformat(),
                "message": "Test notification sent successfully" if success else "Test notification failed"
            }
            
        except Exception as e:
            logger.error("Notification test failed", channel=channel, error=str(e))
            return {
                "success": False,
                "channel": channel,
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
    
    def get_framework_status(self) -> Dict[str, Any]:
        """
        Get current framework status and health
        
        Returns:
            Dictionary with framework status information
        """
        return {
            "framework_version": "1.0.0",
            "environment": self.config.environment.name,
            "spark_version": getattr(self.bronze_tester.spark, 'version', 'unknown'),
            "configuration": {
                "bronze_tests_enabled": self.config.bronze_layer.tests.dict(),
                "quality_gates": {
                    "pass_threshold": self.config.quality_gates.pass_threshold,
                    "warning_threshold": self.config.quality_gates.warning_threshold
                },
                "notifications_enabled": {
                    "email": self.config.notifications.email.enabled,
                    "slack": self.config.notifications.slack.enabled
                }
            },
            "timestamp": datetime.now().isoformat()
        }
