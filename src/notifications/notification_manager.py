"""
Notification Manager

This module handles sending notifications about data quality test results
through various channels including email, Slack, and webhooks.
"""

import smtplib
import json
import requests
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import <PERSON><PERSON><PERSON>ultip<PERSON>
from typing import Dict, Any, List, Optional
from jinja2 import Template
import structlog

from ..config.config_manager import DataTestingConfig
from ..bronze_layer.test_results import BronzeTestSuite
from ..quality_gates.quality_gate import QualityGateResult, QualityGateDecision

logger = structlog.get_logger(__name__)


class NotificationManager:
    """Manages notifications for data quality test results"""
    
    def __init__(self, config: DataTestingConfig):
        """
        Initialize Notification Manager
        
        Args:
            config: Data testing configuration
        """
        self.config = config
        self.notification_config = config.notifications
        
        logger.info("Notification Manager initialized",
                   email_enabled=self.notification_config.email.enabled,
                   slack_enabled=self.notification_config.slack.enabled)
    
    def send_notifications(self, 
                          test_suite: BronzeTestSuite, 
                          quality_gate_result: QualityGateResult) -> Dict[str, bool]:
        """
        Send notifications through all configured channels
        
        Args:
            test_suite: Completed test suite
            quality_gate_result: Quality gate evaluation result
            
        Returns:
            Dictionary with success status for each notification channel
        """
        results = {}
        
        # Determine notification severity
        severity = self._get_notification_severity(quality_gate_result.decision)
        
        logger.info("Sending notifications", 
                   decision=quality_gate_result.decision.value,
                   severity=severity)
        
        # Send email notifications
        if self.notification_config.email.enabled:
            try:
                results['email'] = self._send_email_notification(
                    test_suite, quality_gate_result, severity
                )
            except Exception as e:
                logger.error("Email notification failed", error=str(e))
                results['email'] = False
        
        # Send Slack notifications
        if self.notification_config.slack.enabled:
            try:
                results['slack'] = self._send_slack_notification(
                    test_suite, quality_gate_result, severity
                )
            except Exception as e:
                logger.error("Slack notification failed", error=str(e))
                results['slack'] = False
        
        # Send webhook notifications
        webhook_config = getattr(self.notification_config, 'webhooks', None)
        if webhook_config and webhook_config.enabled:
            try:
                results['webhooks'] = self._send_webhook_notifications(
                    test_suite, quality_gate_result, severity
                )
            except Exception as e:
                logger.error("Webhook notifications failed", error=str(e))
                results['webhooks'] = False
        
        logger.info("Notifications sent", results=results)
        return results
    
    def _get_notification_severity(self, decision: QualityGateDecision) -> str:
        """Determine notification severity based on quality gate decision"""
        severity_map = {
            QualityGateDecision.PASS: "info",
            QualityGateDecision.WARNING: "warning",
            QualityGateDecision.FAIL: "critical",
            QualityGateDecision.QUARANTINE: "critical",
            QualityGateDecision.EMERGENCY: "critical"
        }
        return severity_map.get(decision, "info")
    
    def _send_email_notification(self, 
                                test_suite: BronzeTestSuite, 
                                quality_gate_result: QualityGateResult,
                                severity: str) -> bool:
        """Send email notification"""
        email_config = self.notification_config.email
        
        # Get recipients based on severity
        recipients = email_config.recipients.get(severity, [])
        if not recipients:
            logger.warning("No email recipients configured for severity", severity=severity)
            return True  # Not an error if no recipients
        
        # Create email content
        subject = self._create_email_subject(test_suite, quality_gate_result)
        body = self._create_email_body(test_suite, quality_gate_result)
        
        # Send email
        try:
            msg = MIMEMultipart()
            msg['From'] = email_config.from_address
            msg['To'] = ', '.join(recipients)
            msg['Subject'] = subject
            
            msg.attach(MIMEText(body, 'html'))
            
            # Connect to SMTP server
            with smtplib.SMTP(email_config.smtp_server, email_config.smtp_port) as server:
                if email_config.smtp_username and email_config.smtp_password:
                    server.starttls()
                    server.login(email_config.smtp_username, email_config.smtp_password)
                
                server.send_message(msg)
            
            logger.info("Email notification sent successfully", 
                       recipients=recipients, 
                       subject=subject)
            return True
            
        except Exception as e:
            logger.error("Failed to send email notification", error=str(e))
            return False
    
    def _create_email_subject(self, test_suite: BronzeTestSuite, quality_gate_result: QualityGateResult) -> str:
        """Create email subject line"""
        status_emoji = {
            QualityGateDecision.PASS: "✅",
            QualityGateDecision.WARNING: "⚠️",
            QualityGateDecision.FAIL: "❌",
            QualityGateDecision.QUARANTINE: "🔒",
            QualityGateDecision.EMERGENCY: "🚨"
        }
        
        emoji = status_emoji.get(quality_gate_result.decision, "📊")
        table_name = test_suite.table_path.split('/')[-1]
        
        return f"{emoji} Data Quality Alert: {table_name} - {quality_gate_result.decision.value}"
    
    def _create_email_body(self, test_suite: BronzeTestSuite, quality_gate_result: QualityGateResult) -> str:
        """Create HTML email body"""
        template_str = """
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background-color: {{ header_color }}; color: white; padding: 15px; border-radius: 5px; }
                .summary { background-color: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
                .metrics { margin: 15px 0; }
                .metric { margin: 5px 0; }
                .recommendations { background-color: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px; }
                .footer { margin-top: 20px; font-size: 12px; color: #666; }
                table { border-collapse: collapse; width: 100%; margin: 10px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
            </style>
        </head>
        <body>
            <div class="header">
                <h2>Data Quality Report</h2>
                <p><strong>Table:</strong> {{ table_path }}</p>
                <p><strong>Decision:</strong> {{ decision }}</p>
                <p><strong>Overall Score:</strong> {{ overall_score }}%</p>
            </div>
            
            <div class="summary">
                <h3>Test Summary</h3>
                <div class="metrics">
                    <div class="metric"><strong>Total Tests:</strong> {{ summary.total_tests }}</div>
                    <div class="metric"><strong>Passed:</strong> {{ summary.passed }}</div>
                    <div class="metric"><strong>Warnings:</strong> {{ summary.warnings }}</div>
                    <div class="metric"><strong>Failed:</strong> {{ summary.failed }}</div>
                    <div class="metric"><strong>Execution Time:</strong> {{ execution_time }}s</div>
                </div>
            </div>
            
            {% if test_results %}
            <h3>Test Results</h3>
            <table>
                <tr>
                    <th>Test Name</th>
                    <th>Status</th>
                    <th>Score</th>
                    <th>Message</th>
                </tr>
                {% for test in test_results %}
                <tr>
                    <td>{{ test.test_name }}</td>
                    <td>{{ test.status }}</td>
                    <td>{{ "%.1f"|format(test.overall_score * 100) }}%</td>
                    <td>{{ test.error_message or "OK" }}</td>
                </tr>
                {% endfor %}
            </table>
            {% endif %}
            
            {% if recommendations %}
            <div class="recommendations">
                <h3>Recommendations</h3>
                <ul>
                {% for rec in recommendations %}
                    <li>{{ rec }}</li>
                {% endfor %}
                </ul>
            </div>
            {% endif %}
            
            <div class="footer">
                <p>Generated by Medallion Architecture Data Testing Framework</p>
                <p>Execution ID: {{ execution_id }}</p>
                <p>Timestamp: {{ timestamp }}</p>
            </div>
        </body>
        </html>
        """
        
        # Determine header color based on decision
        color_map = {
            QualityGateDecision.PASS: "#28a745",
            QualityGateDecision.WARNING: "#ffc107",
            QualityGateDecision.FAIL: "#dc3545",
            QualityGateDecision.QUARANTINE: "#6f42c1",
            QualityGateDecision.EMERGENCY: "#dc3545"
        }
        
        template = Template(template_str)
        return template.render(
            table_path=test_suite.table_path,
            decision=quality_gate_result.decision.value,
            overall_score=round(quality_gate_result.overall_score * 100, 1),
            summary=test_suite.get_summary(),
            execution_time=round(test_suite.get_execution_time(), 1),
            test_results=test_suite.test_results,
            recommendations=quality_gate_result.recommendations,
            execution_id=test_suite.execution_id,
            timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            header_color=color_map.get(quality_gate_result.decision, "#6c757d")
        )
    
    def _send_slack_notification(self, 
                                test_suite: BronzeTestSuite, 
                                quality_gate_result: QualityGateResult,
                                severity: str) -> bool:
        """Send Slack notification"""
        slack_config = self.notification_config.slack
        
        if not slack_config.webhook_url:
            logger.warning("Slack webhook URL not configured")
            return False
        
        # Create Slack message
        message = self._create_slack_message(test_suite, quality_gate_result, severity)
        
        try:
            response = requests.post(
                slack_config.webhook_url,
                json=message,
                timeout=30
            )
            response.raise_for_status()
            
            logger.info("Slack notification sent successfully")
            return True
            
        except Exception as e:
            logger.error("Failed to send Slack notification", error=str(e))
            return False

    def _create_slack_message(self,
                             test_suite: BronzeTestSuite,
                             quality_gate_result: QualityGateResult,
                             severity: str) -> Dict[str, Any]:
        """Create Slack message payload"""
        slack_config = self.notification_config.slack

        # Status emoji and color
        status_map = {
            QualityGateDecision.PASS: {"emoji": "✅", "color": "good"},
            QualityGateDecision.WARNING: {"emoji": "⚠️", "color": "warning"},
            QualityGateDecision.FAIL: {"emoji": "❌", "color": "danger"},
            QualityGateDecision.QUARANTINE: {"emoji": "🔒", "color": "danger"},
            QualityGateDecision.EMERGENCY: {"emoji": "🚨", "color": "danger"}
        }

        status_info = status_map.get(quality_gate_result.decision, {"emoji": "📊", "color": "good"})
        table_name = test_suite.table_path.split('/')[-1]

        # Create main message
        text = f"{status_info['emoji']} Data Quality Alert: {table_name} - {quality_gate_result.decision.value}"

        # Add mentions for critical issues
        if (severity == "critical" and slack_config.mention_on_critical and
            slack_config.critical_mentions):
            mentions = " ".join(slack_config.critical_mentions)
            text = f"{mentions} {text}"

        # Create attachment with details
        attachment = {
            "color": status_info["color"],
            "title": f"Data Quality Report - {table_name}",
            "fields": [
                {
                    "title": "Overall Score",
                    "value": f"{quality_gate_result.overall_score:.1%}",
                    "short": True
                },
                {
                    "title": "Decision",
                    "value": quality_gate_result.decision.value,
                    "short": True
                },
                {
                    "title": "Table Path",
                    "value": test_suite.table_path,
                    "short": False
                }
            ],
            "footer": "Medallion Data Testing Framework",
            "ts": int(datetime.now().timestamp())
        }

        # Add test summary if requested
        if slack_config.include_details:
            summary = test_suite.get_summary()
            attachment["fields"].extend([
                {
                    "title": "Tests Summary",
                    "value": f"Total: {summary['total_tests']}, Passed: {summary['passed']}, "
                            f"Warnings: {summary['warnings']}, Failed: {summary['failed']}",
                    "short": False
                }
            ])

            # Add top recommendations
            if quality_gate_result.recommendations:
                top_recommendations = quality_gate_result.recommendations[:3]
                rec_text = "\n".join([f"• {rec}" for rec in top_recommendations])
                attachment["fields"].append({
                    "title": "Key Recommendations",
                    "value": rec_text,
                    "short": False
                })

        message = {
            "text": text,
            "username": slack_config.username,
            "channel": slack_config.channel,
            "attachments": [attachment]
        }

        return message

    def _send_webhook_notifications(self,
                                   test_suite: BronzeTestSuite,
                                   quality_gate_result: QualityGateResult,
                                   severity: str) -> bool:
        """Send webhook notifications to custom endpoints"""
        webhook_config = getattr(self.notification_config, 'webhooks', None)
        if not webhook_config or not webhook_config.endpoints:
            return True  # No webhooks configured

        success_count = 0
        total_webhooks = len(webhook_config.endpoints)

        # Create webhook payload
        payload = {
            "event_type": "data_quality_test_completed",
            "timestamp": datetime.now().isoformat(),
            "severity": severity,
            "table_path": test_suite.table_path,
            "execution_id": test_suite.execution_id,
            "quality_gate": {
                "decision": quality_gate_result.decision.value,
                "overall_score": quality_gate_result.overall_score,
                "threshold_met": quality_gate_result.threshold_met,
                "quarantine_required": quality_gate_result.quarantine_required,
                "emergency_escalation": quality_gate_result.emergency_escalation
            },
            "test_summary": test_suite.get_summary(),
            "recommendations": quality_gate_result.recommendations
        }

        for endpoint in webhook_config.endpoints:
            try:
                headers = {"Content-Type": "application/json"}
                if endpoint.get("headers"):
                    headers.update(endpoint["headers"])

                response = requests.post(
                    endpoint["url"],
                    json=payload,
                    headers=headers,
                    timeout=30
                )
                response.raise_for_status()

                logger.info("Webhook notification sent successfully",
                           endpoint_name=endpoint.get("name", "unknown"))
                success_count += 1

            except Exception as e:
                logger.error("Failed to send webhook notification",
                           endpoint_name=endpoint.get("name", "unknown"),
                           error=str(e))

                # Retry logic if configured
                retry_attempts = endpoint.get("retry_attempts", 0)
                for attempt in range(retry_attempts):
                    try:
                        response = requests.post(
                            endpoint["url"],
                            json=payload,
                            headers=headers,
                            timeout=30
                        )
                        response.raise_for_status()
                        logger.info("Webhook notification retry successful",
                                   endpoint_name=endpoint.get("name", "unknown"),
                                   attempt=attempt + 1)
                        success_count += 1
                        break
                    except Exception as retry_e:
                        logger.warning("Webhook notification retry failed",
                                     endpoint_name=endpoint.get("name", "unknown"),
                                     attempt=attempt + 1,
                                     error=str(retry_e))

        return success_count == total_webhooks

    def send_test_notification(self, channel: str = "all") -> bool:
        """
        Send a test notification to verify configuration

        Args:
            channel: Which channel to test ("email", "slack", "webhooks", or "all")

        Returns:
            True if test notification was sent successfully
        """
        logger.info("Sending test notification", channel=channel)

        # Create dummy test data
        from ..bronze_layer.test_results import BronzeTestSuite, TestResult, TestStatus
        from ..quality_gates.quality_gate import QualityGateResult, QualityGateDecision

        test_suite = BronzeTestSuite(
            table_path="test/table/path",
            execution_id="test-execution-id",
            start_time=datetime.now()
        )
        test_suite.end_time = datetime.now()

        # Add a dummy test result
        test_result = TestResult(
            test_name="test_notification",
            status=TestStatus.PASS,
            execution_time_seconds=1.0
        )
        test_suite.add_test_result(test_result)

        quality_gate_result = QualityGateResult(
            decision=QualityGateDecision.PASS,
            overall_score=0.95,
            threshold_met=True,
            reasons=["Test notification"],
            recommendations=["This is a test notification"]
        )

        try:
            if channel == "all":
                results = self.send_notifications(test_suite, quality_gate_result)
                return all(results.values())
            elif channel == "email" and self.notification_config.email.enabled:
                return self._send_email_notification(test_suite, quality_gate_result, "info")
            elif channel == "slack" and self.notification_config.slack.enabled:
                return self._send_slack_notification(test_suite, quality_gate_result, "info")
            elif channel == "webhooks":
                return self._send_webhook_notifications(test_suite, quality_gate_result, "info")
            else:
                logger.warning("Invalid channel or channel not enabled", channel=channel)
                return False

        except Exception as e:
            logger.error("Test notification failed", channel=channel, error=str(e))
            return False
