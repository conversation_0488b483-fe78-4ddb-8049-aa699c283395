"""
Configuration Management for Data Testing Framework

This module handles loading, validation, and management of configuration
settings for the medallion architecture data testing framework.
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path
from pydantic import BaseModel, Field, validator
from dotenv import load_dotenv
import structlog

logger = structlog.get_logger(__name__)


class BronzeLayerConfig(BaseModel):
    """Configuration for Bronze layer testing"""
    
    class Tests(BaseModel):
        file_integrity: bool = True
        schema_validation: bool = True
        record_counting: bool = True
        timestamp_validation: bool = True
        metadata_preservation: bool = True
        duplicate_detection: bool = True
    
    class Thresholds(BaseModel):
        completeness_min: float = Field(ge=0.0, le=1.0, default=0.95)
        accuracy_min: float = Field(ge=0.0, le=1.0, default=0.99)
        consistency_min: float = Field(ge=0.0, le=1.0, default=0.98)
        validity_min: float = Field(ge=0.0, le=1.0, default=0.97)
        timeliness_max_hours: int = Field(ge=1, default=24)
        uniqueness_min: float = Field(ge=0.0, le=1.0, default=0.99)
    
    class FileProcessing(BaseModel):
        max_file_size_gb: int = Field(ge=1, default=10)
        supported_formats: list = ["parquet", "delta", "json", "csv"]
        encoding: str = "utf-8"
    
    class SchemaValidation(BaseModel):
        strict_mode: bool = False
        allow_new_columns: bool = True
        allow_missing_columns: bool = False
    
    tests: Tests = Tests()
    thresholds: Thresholds = Thresholds()
    file_processing: FileProcessing = FileProcessing()
    schema_validation: SchemaValidation = SchemaValidation()


class QualityGatesConfig(BaseModel):
    """Configuration for Quality Gates"""
    
    class Escalation(BaseModel):
        auto_recovery_attempts: int = Field(ge=0, default=3)
        escalation_delay_minutes: int = Field(ge=1, default=15)
        emergency_contact_threshold: float = Field(ge=0.0, le=1.0, default=0.50)
    
    class Quarantine(BaseModel):
        enabled: bool = True
        retention_days: int = Field(ge=1, default=30)
        auto_review_enabled: bool = True
        max_quarantine_size_gb: int = Field(ge=1, default=100)
    
    pass_threshold: float = Field(ge=0.0, le=1.0, default=0.95)
    warning_threshold: float = Field(ge=0.0, le=1.0, default=0.85)
    escalation: Escalation = Escalation()
    quarantine: Quarantine = Quarantine()
    
    @validator('warning_threshold')
    def warning_less_than_pass(cls, v, values):
        if 'pass_threshold' in values and v >= values['pass_threshold']:
            raise ValueError('warning_threshold must be less than pass_threshold')
        return v


class NotificationsConfig(BaseModel):
    """Configuration for Notifications"""
    
    class Email(BaseModel):
        enabled: bool = True
        smtp_server: str = "localhost"
        smtp_port: int = 587
        smtp_username: str = ""
        smtp_password: str = ""
        from_address: str = ""
        recipients: Dict[str, list] = {
            "critical": [],
            "warning": [],
            "info": []
        }
    
    class Slack(BaseModel):
        enabled: bool = False
        webhook_url: str = ""
        channel: str = "#data-quality"
        username: str = "DataQualityBot"
        include_details: bool = True
        include_charts: bool = False
        mention_on_critical: bool = True
        critical_mentions: list = []
    
    email: Email = Email()
    slack: Slack = Slack()


class EnvironmentConfig(BaseModel):
    """Environment configuration"""
    name: str = "development"
    databricks_workspace_url: str = ""
    catalog_name: str = ""
    schema_name: str = ""


class DataTestingConfig(BaseModel):
    """Main configuration model"""
    environment: EnvironmentConfig = EnvironmentConfig()
    bronze_layer: BronzeLayerConfig = BronzeLayerConfig()
    quality_gates: QualityGatesConfig = QualityGatesConfig()
    notifications: NotificationsConfig = NotificationsConfig()


class ConfigManager:
    """Manages configuration loading and validation"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize ConfigManager
        
        Args:
            config_path: Path to configuration file. If None, uses default locations.
        """
        self.config_path = self._resolve_config_path(config_path)
        self.config: Optional[DataTestingConfig] = None
        
        # Load environment variables
        load_dotenv()
        
        logger.info("ConfigManager initialized", config_path=str(self.config_path))
    
    def _resolve_config_path(self, config_path: Optional[str]) -> Path:
        """Resolve configuration file path"""
        if config_path:
            return Path(config_path)
        
        # Try default locations
        possible_paths = [
            Path("config/config.yaml"),
            Path("config/config.yml"),
            Path("config.yaml"),
            Path("config.yml")
        ]
        
        for path in possible_paths:
            if path.exists():
                return path
        
        # If no config found, use template
        template_path = Path("config/config.template.yaml")
        if template_path.exists():
            logger.warning(
                "No config file found, using template. "
                "Copy config.template.yaml to config.yaml and customize."
            )
            return template_path
        
        raise FileNotFoundError("No configuration file found")
    
    def load_config(self) -> DataTestingConfig:
        """Load and validate configuration"""
        try:
            with open(self.config_path, 'r') as f:
                raw_config = yaml.safe_load(f)
            
            # Substitute environment variables
            raw_config = self._substitute_env_vars(raw_config)
            
            # Validate and create config object
            self.config = DataTestingConfig(**raw_config)
            
            logger.info("Configuration loaded successfully", 
                       environment=self.config.environment.name)
            
            return self.config
            
        except Exception as e:
            logger.error("Failed to load configuration", 
                        config_path=str(self.config_path), 
                        error=str(e))
            raise
    
    def _substitute_env_vars(self, obj: Any) -> Any:
        """Recursively substitute environment variables in configuration"""
        if isinstance(obj, dict):
            return {k: self._substitute_env_vars(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._substitute_env_vars(item) for item in obj]
        elif isinstance(obj, str) and obj.startswith("${") and obj.endswith("}"):
            env_var = obj[2:-1]
            return os.getenv(env_var, obj)
        else:
            return obj
    
    def get_config(self) -> DataTestingConfig:
        """Get current configuration, loading if necessary"""
        if self.config is None:
            self.load_config()
        return self.config
    
    def reload_config(self) -> DataTestingConfig:
        """Reload configuration from file"""
        self.config = None
        return self.load_config()
    
    def validate_config(self) -> bool:
        """Validate current configuration"""
        try:
            config = self.get_config()
            logger.info("Configuration validation successful")
            return True
        except Exception as e:
            logger.error("Configuration validation failed", error=str(e))
            return False
