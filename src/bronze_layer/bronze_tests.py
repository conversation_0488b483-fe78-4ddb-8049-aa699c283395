"""
Bronze Layer Test Implementations

This module contains the actual test implementations for Bronze layer
data quality validation according to the medallion architecture strategy.
"""

import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, count, isnan, isnull, when, sum as spark_sum
from pyspark.sql.types import StructType
import structlog

from .test_results import TestResult, TestMetric, TestStatus, QualityDimension

logger = structlog.get_logger(__name__)


class BronzeLayerTests:
    """Bronze layer test implementations"""
    
    def __init__(self, spark: SparkSession, config: Dict[str, Any]):
        """
        Initialize Bronze layer tests
        
        Args:
            spark: SparkSession instance
            config: Configuration dictionary
        """
        self.spark = spark
        self.config = config
        self.bronze_config = config.get('bronze_layer', {})
        self.thresholds = self.bronze_config.get('thresholds', {})
        
    def test_file_integrity(self, df: DataFrame, table_path: str) -> TestResult:
        """
        Test file integrity - verify files weren't corrupted during transfer
        
        Args:
            df: DataFrame to test
            table_path: Path to the table being tested
            
        Returns:
            TestResult with file integrity validation results
        """
        start_time = time.time()
        test_name = "file_integrity"
        
        try:
            logger.info("Starting file integrity test", table_path=table_path)
            
            # Check if DataFrame can be read without errors
            try:
                record_count = df.count()
                integrity_score = 1.0  # If we can count, file is readable
                status = TestStatus.PASS
                message = f"File integrity verified. Records: {record_count}"
                
            except Exception as e:
                integrity_score = 0.0
                status = TestStatus.FAIL
                message = f"File integrity check failed: {str(e)}"
                record_count = 0
            
            # Create metrics
            metrics = [
                TestMetric(
                    name="file_readable",
                    value=integrity_score,
                    threshold=1.0,
                    status=status,
                    message=message,
                    dimension=QualityDimension.VALIDITY
                ),
                TestMetric(
                    name="record_count",
                    value=1.0 if record_count > 0 else 0.0,
                    status=TestStatus.PASS if record_count > 0 else TestStatus.FAIL,
                    message=f"Total records: {record_count}",
                    dimension=QualityDimension.COMPLETENESS
                )
            ]
            
            execution_time = time.time() - start_time
            
            return TestResult(
                test_name=test_name,
                status=status,
                execution_time_seconds=execution_time,
                metrics=metrics,
                details={
                    "table_path": table_path,
                    "record_count": record_count
                }
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error("File integrity test failed", error=str(e))
            
            return TestResult(
                test_name=test_name,
                status=TestStatus.ERROR,
                execution_time_seconds=execution_time,
                error_message=str(e)
            )
    
    def test_schema_validation(self, df: DataFrame, expected_schema: Optional[StructType] = None) -> TestResult:
        """
        Test schema validation - ensure data structure matches expectations
        
        Args:
            df: DataFrame to test
            expected_schema: Expected schema (optional)
            
        Returns:
            TestResult with schema validation results
        """
        start_time = time.time()
        test_name = "schema_validation"
        
        try:
            logger.info("Starting schema validation test")

            # If no expected schema is provided, skip the test
            if expected_schema is None:
                execution_time = time.time() - start_time
                return TestResult(
                    test_name=test_name,
                    status=TestStatus.SKIPPED,  # Return SKIPPED status
                    execution_time_seconds=execution_time,
                    metrics=[TestMetric(
                        name="schema_validation_skipped",
                        value=1.0,
                        status=TestStatus.SKIPPED,
                        message="Schema validation skipped - no expected schema provided",
                        dimension=QualityDimension.VALIDITY
                    )]
                )

            current_schema = df.schema
            schema_config = self.bronze_config.get('schema_validation', {})

            metrics = []
            overall_status = TestStatus.PASS

            # If expected schema is provided, compare
            if expected_schema:
                expected_columns = set(field.name for field in expected_schema.fields)
                current_columns = set(field.name for field in current_schema.fields)
                
                # Check for missing columns
                missing_columns = expected_columns - current_columns
                if missing_columns and not schema_config.get('allow_missing_columns', False):
                    overall_status = TestStatus.FAIL
                    metrics.append(TestMetric(
                        name="missing_columns",
                        value=float(len(missing_columns)),
                        threshold=0.0,
                        status=TestStatus.FAIL,
                        message=f"Missing columns: {list(missing_columns)}",
                        dimension=QualityDimension.COMPLETENESS
                    ))
                
                # Check for new columns
                new_columns = current_columns - expected_columns
                if new_columns:
                    if schema_config.get('allow_new_columns', True):
                        status = TestStatus.WARNING
                        message = f"New columns detected: {list(new_columns)}"
                    else:
                        status = TestStatus.FAIL
                        message = f"Unexpected columns: {list(new_columns)}"
                        overall_status = TestStatus.FAIL
                    
                    metrics.append(TestMetric(
                        name="new_columns",
                        value=float(len(new_columns)),
                        threshold=0.0,
                        status=status,
                        message=message,
                        dimension=QualityDimension.CONSISTENCY
                    ))
                
                # Schema compatibility score
                total_expected = len(expected_columns)
                matching_columns = len(expected_columns & current_columns)
                compatibility_score = matching_columns / total_expected if total_expected > 0 else 1.0
                
                metrics.append(TestMetric(
                    name="schema_compatibility",
                    value=compatibility_score,
                    threshold=0.95,
                    status=TestStatus.PASS if compatibility_score >= 0.95 else TestStatus.WARNING,
                    message=f"Schema compatibility: {compatibility_score:.2%}",
                    dimension=QualityDimension.CONSISTENCY
                ))
            
            execution_time = time.time() - start_time
            
            return TestResult(
                test_name=test_name,
                status=overall_status,
                execution_time_seconds=execution_time,
                metrics=metrics,
                details={
                    "current_schema": [{"name": f.name, "type": str(f.dataType)} for f in current_schema.fields],
                    "column_count": len(current_schema.fields)
                }
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error("Schema validation test failed", error=str(e))
            
            return TestResult(
                test_name=test_name,
                status=TestStatus.ERROR,
                execution_time_seconds=execution_time,
                error_message=str(e)
            )
    
    def test_record_counting(self, df: DataFrame, expected_count: Optional[int] = None) -> TestResult:
        """
        Test record counting - verify all expected records were received
        
        Args:
            df: DataFrame to test
            expected_count: Expected number of records (optional)
            
        Returns:
            TestResult with record counting validation results
        """
        start_time = time.time()
        test_name = "record_counting"
        
        try:
            logger.info("Starting record counting test")
            
            actual_count = df.count()
            metrics = []
            overall_status = TestStatus.PASS
            
            # Basic record count metric (score based on having records)
            metrics.append(TestMetric(
                name="total_records",
                value=1.0 if actual_count > 0 else 0.0,
                status=TestStatus.PASS if actual_count > 0 else TestStatus.FAIL,
                message=f"Total records: {actual_count}",
                dimension=QualityDimension.COMPLETENESS
            ))
            
            # If expected count is provided, validate
            if expected_count is not None:
                count_difference = abs(actual_count - expected_count)
                count_accuracy = 1.0 - (count_difference / max(expected_count, 1))
                
                if actual_count == expected_count:
                    status = TestStatus.PASS
                    message = f"Record count matches expected: {actual_count}"
                elif count_accuracy >= 0.95:
                    status = TestStatus.WARNING
                    message = f"Record count close to expected. Got: {actual_count}, Expected: {expected_count}"
                else:
                    status = TestStatus.FAIL
                    message = f"Record count mismatch. Got: {actual_count}, Expected: {expected_count}"
                    overall_status = TestStatus.FAIL
                
                metrics.append(TestMetric(
                    name="count_accuracy",
                    value=count_accuracy,
                    threshold=0.95,
                    status=status,
                    message=message,
                    dimension=QualityDimension.ACCURACY
                ))
            
            # Check for empty dataset
            if actual_count == 0:
                overall_status = TestStatus.WARNING
                metrics.append(TestMetric(
                    name="empty_dataset",
                    value=0.0,
                    threshold=1.0,
                    status=TestStatus.WARNING,
                    message="Dataset is empty",
                    dimension=QualityDimension.COMPLETENESS
                ))
            
            execution_time = time.time() - start_time
            
            return TestResult(
                test_name=test_name,
                status=overall_status,
                execution_time_seconds=execution_time,
                metrics=metrics,
                details={
                    "actual_count": actual_count,
                    "expected_count": expected_count
                }
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error("Record counting test failed", error=str(e))
            
            return TestResult(
                test_name=test_name,
                status=TestStatus.ERROR,
                execution_time_seconds=execution_time,
                error_message=str(e)
            )

    def test_timestamp_validation(self, df: DataFrame, timestamp_column: str = "ingestion_timestamp") -> TestResult:
        """
        Test timestamp validation - check data arrival times are logical

        Args:
            df: DataFrame to test
            timestamp_column: Name of timestamp column to validate

        Returns:
            TestResult with timestamp validation results
        """
        start_time = time.time()
        test_name = "timestamp_validation"

        try:
            logger.info("Starting timestamp validation test", timestamp_column=timestamp_column)

            metrics = []
            overall_status = TestStatus.PASS

            # Check if timestamp column exists
            if timestamp_column not in df.columns:
                execution_time = time.time() - start_time
                return TestResult(
                    test_name=test_name,
                    status=TestStatus.FAIL,
                    execution_time_seconds=execution_time,
                    error_message=f"Timestamp column '{timestamp_column}' not found"
                )

            # Get timestamp statistics
            timestamp_stats = df.select(
                col(timestamp_column).alias("ts")
            ).filter(col("ts").isNotNull()).agg(
                {"ts": "min", "ts": "max", "ts": "count"}
            ).collect()[0]

            min_timestamp = timestamp_stats[0]
            max_timestamp = timestamp_stats[1]
            valid_timestamp_count = timestamp_stats[2]
            total_count = df.count()

            # Calculate timeliness metrics
            current_time = datetime.now()
            max_age_hours = self.thresholds.get('timeliness_max_hours', 24)

            if max_timestamp:
                data_age_hours = (current_time - max_timestamp).total_seconds() / 3600
                timeliness_score = max(0.0, 1.0 - (data_age_hours / max_age_hours))

                if data_age_hours <= max_age_hours:
                    status = TestStatus.PASS
                    message = f"Data is timely. Age: {data_age_hours:.1f} hours"
                else:
                    status = TestStatus.WARNING
                    message = f"Data may be stale. Age: {data_age_hours:.1f} hours"
                    if overall_status == TestStatus.PASS:
                        overall_status = TestStatus.WARNING

                metrics.append(TestMetric(
                    name="timeliness_score",
                    value=timeliness_score,
                    threshold=0.8,
                    status=status,
                    message=message,
                    dimension=QualityDimension.TIMELINESS
                ))

            # Timestamp completeness
            completeness = valid_timestamp_count / total_count if total_count > 0 else 0.0
            completeness_threshold = self.thresholds.get('completeness_min', 0.95)

            if completeness >= completeness_threshold:
                status = TestStatus.PASS
            elif completeness >= 0.8:
                status = TestStatus.WARNING
                if overall_status == TestStatus.PASS:
                    overall_status = TestStatus.WARNING
            else:
                status = TestStatus.FAIL
                overall_status = TestStatus.FAIL

            metrics.append(TestMetric(
                name="timestamp_completeness",
                value=completeness,
                threshold=completeness_threshold,
                status=status,
                message=f"Timestamp completeness: {completeness:.2%}",
                dimension=QualityDimension.COMPLETENESS
            ))

            execution_time = time.time() - start_time

            return TestResult(
                test_name=test_name,
                status=overall_status,
                execution_time_seconds=execution_time,
                metrics=metrics,
                details={
                    "timestamp_column": timestamp_column,
                    "min_timestamp": str(min_timestamp) if min_timestamp else None,
                    "max_timestamp": str(max_timestamp) if max_timestamp else None,
                    "valid_timestamps": valid_timestamp_count,
                    "total_records": total_count
                }
            )

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error("Timestamp validation test failed", error=str(e))

            return TestResult(
                test_name=test_name,
                status=TestStatus.ERROR,
                execution_time_seconds=execution_time,
                error_message=str(e)
            )

    def test_metadata_preservation(self, df: DataFrame, source_metadata: Dict[str, Any]) -> TestResult:
        """
        Test metadata preservation - keep all information about data sources

        Args:
            df: DataFrame to test
            source_metadata: Expected source metadata

        Returns:
            TestResult with metadata preservation validation results
        """
        start_time = time.time()
        test_name = "metadata_preservation"

        try:
            logger.info("Starting metadata preservation test")

            metrics = []
            overall_status = TestStatus.PASS

            # Check for metadata columns
            metadata_columns = [col for col in df.columns if col.startswith('_metadata_')]
            metadata_score = len(metadata_columns) / max(len(source_metadata), 1) if source_metadata else 1.0

            if metadata_score >= 0.9:
                status = TestStatus.PASS
                message = f"Metadata well preserved. Found {len(metadata_columns)} metadata columns"
            elif metadata_score >= 0.7:
                status = TestStatus.WARNING
                message = f"Some metadata missing. Found {len(metadata_columns)} metadata columns"
                overall_status = TestStatus.WARNING
            else:
                status = TestStatus.FAIL
                message = f"Significant metadata missing. Found {len(metadata_columns)} metadata columns"
                overall_status = TestStatus.FAIL

            metrics.append(TestMetric(
                name="metadata_preservation_score",
                value=metadata_score,
                threshold=0.9,
                status=status,
                message=message,
                dimension=QualityDimension.COMPLETENESS
            ))

            # Check for source system information
            source_columns = [col for col in df.columns if 'source' in col.lower()]
            if source_columns:
                metrics.append(TestMetric(
                    name="source_tracking",
                    value=1.0,
                    status=TestStatus.PASS,
                    message=f"Source tracking columns found: {source_columns}",
                    dimension=QualityDimension.VALIDITY
                ))
            else:
                metrics.append(TestMetric(
                    name="source_tracking",
                    value=0.0,
                    threshold=1.0,
                    status=TestStatus.WARNING,
                    message="No source tracking columns found",
                    dimension=QualityDimension.VALIDITY
                ))
                if overall_status == TestStatus.PASS:
                    overall_status = TestStatus.WARNING

            execution_time = time.time() - start_time

            return TestResult(
                test_name=test_name,
                status=overall_status,
                execution_time_seconds=execution_time,
                metrics=metrics,
                details={
                    "metadata_columns": metadata_columns,
                    "source_columns": source_columns,
                    "expected_metadata": source_metadata
                }
            )

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error("Metadata preservation test failed", error=str(e))

            return TestResult(
                test_name=test_name,
                status=TestStatus.ERROR,
                execution_time_seconds=execution_time,
                error_message=str(e)
            )
