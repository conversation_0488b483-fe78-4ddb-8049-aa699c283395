"""
Bronze Layer Tester

Main class for orchestrating Bronze layer data quality tests
according to the medallion architecture strategy.
"""

import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
from pyspark.sql import SparkSession, DataFrame
from pyspark.sql.types import StructType
import structlog

from ..config.config_manager import Config<PERSON>anager, DataTestingConfig
from .bronze_tests import BronzeLayerTests
from .test_results import BronzeTestSuite, TestResult, TestStatus

logger = structlog.get_logger(__name__)


class BronzeTester:
    """Main Bronze layer testing orchestrator"""
    
    def __init__(self, config_path: Optional[str] = None, spark: Optional[SparkSession] = None):
        """
        Initialize Bronze layer tester
        
        Args:
            config_path: Path to configuration file
            spark: SparkSession instance (will create if not provided)
        """
        self.config_manager = ConfigManager(config_path)
        self.config = self.config_manager.get_config()
        
        # Initialize Spark session
        if spark is None:
            self.spark = self._create_spark_session()
        else:
            self.spark = spark
        
        # Initialize test implementations
        self.tests = BronzeLayerTests(self.spark, self.config.dict())
        
        logger.info("BronzeTester initialized", 
                   environment=self.config.environment.name)
    
    def _create_spark_session(self) -> SparkSession:
        """Create Spark session with appropriate configuration"""
        builder = SparkSession.builder.appName("BronzeLayerTesting")
        
        # Apply performance settings from config
        perf_config = self.config.dict().get('performance', {}).get('spark', {})
        for key, value in perf_config.items():
            spark_key = f"spark.executor.{key}" if key in ['memory', 'cores'] else f"spark.{key}"
            builder = builder.config(spark_key, str(value))
        
        # Enable Delta Lake
        builder = builder.config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension")
        builder = builder.config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog")
        
        return builder.getOrCreate()
    
    def run_tests(self, 
                  table_path: str,
                  expected_schema: Optional[StructType] = None,
                  expected_count: Optional[int] = None,
                  source_metadata: Optional[Dict[str, Any]] = None,
                  timestamp_column: str = "ingestion_timestamp") -> BronzeTestSuite:
        """
        Run complete Bronze layer test suite
        
        Args:
            table_path: Path to the table/data to test
            expected_schema: Expected schema for validation
            expected_count: Expected number of records
            source_metadata: Source system metadata
            timestamp_column: Name of timestamp column for validation
            
        Returns:
            BronzeTestSuite with complete test results
        """
        execution_id = str(uuid.uuid4())
        start_time = datetime.now()
        
        logger.info("Starting Bronze layer test suite", 
                   table_path=table_path, 
                   execution_id=execution_id)
        
        # Initialize test suite
        test_suite = BronzeTestSuite(
            table_path=table_path,
            execution_id=execution_id,
            start_time=start_time,
            metadata={
                "config_environment": self.config.environment.name,
                "spark_version": self.spark.version,
                "expected_schema": str(expected_schema) if expected_schema else None,
                "expected_count": expected_count,
                "timestamp_column": timestamp_column
            }
        )
        
        try:
            # Load the data
            df = self._load_data(table_path)
            if df is None:
                test_suite.overall_status = TestStatus.ERROR
                test_suite.end_time = datetime.now()
                return test_suite
            
            # Get test configuration
            test_config = self.config.bronze_layer.tests
            
            # Run individual tests based on configuration
            if test_config.file_integrity:
                result = self.tests.test_file_integrity(df, table_path)
                test_suite.add_test_result(result)
                logger.info("File integrity test completed", 
                           status=result.status.value, 
                           score=result.get_overall_score())
            
            if test_config.schema_validation:
                result = self.tests.test_schema_validation(df, expected_schema)
                test_suite.add_test_result(result)
                logger.info("Schema validation test completed", 
                           status=result.status.value, 
                           score=result.get_overall_score())
            
            if test_config.record_counting:
                result = self.tests.test_record_counting(df, expected_count)
                test_suite.add_test_result(result)
                logger.info("Record counting test completed", 
                           status=result.status.value, 
                           score=result.get_overall_score())
            
            if test_config.timestamp_validation:
                result = self.tests.test_timestamp_validation(df, timestamp_column)
                test_suite.add_test_result(result)
                logger.info("Timestamp validation test completed", 
                           status=result.status.value, 
                           score=result.get_overall_score())
            
            if test_config.metadata_preservation and source_metadata:
                result = self.tests.test_metadata_preservation(df, source_metadata)
                test_suite.add_test_result(result)
                logger.info("Metadata preservation test completed", 
                           status=result.status.value, 
                           score=result.get_overall_score())
            
            # TODO: Add duplicate detection test when implemented
            
        except Exception as e:
            logger.error("Bronze layer test suite failed", error=str(e))
            test_suite.overall_status = TestStatus.ERROR
            # Add error result
            error_result = TestResult(
                test_name="test_suite_execution",
                status=TestStatus.ERROR,
                execution_time_seconds=0.0,
                error_message=str(e)
            )
            test_suite.add_test_result(error_result)
        
        finally:
            test_suite.end_time = datetime.now()
            
            logger.info("Bronze layer test suite completed", 
                       execution_id=execution_id,
                       overall_status=test_suite.overall_status.value,
                       overall_score=test_suite.get_overall_score(),
                       execution_time=test_suite.get_execution_time())
        
        return test_suite
    
    def _load_data(self, table_path: str) -> Optional[DataFrame]:
        """
        Load data from the specified path
        
        Args:
            table_path: Path to the data
            
        Returns:
            DataFrame or None if loading failed
        """
        try:
            logger.info("Loading data", table_path=table_path)
            
            # Determine format and load accordingly
            if table_path.endswith('.parquet'):
                df = self.spark.read.parquet(table_path)
            elif table_path.endswith('.json'):
                df = self.spark.read.json(table_path)
            elif table_path.endswith('.csv'):
                df = self.spark.read.option("header", "true").csv(table_path)
            elif 'delta' in table_path.lower() or table_path.startswith('dbfs:'):
                # Assume Delta table
                df = self.spark.read.format("delta").load(table_path)
            else:
                # Try to read as Delta table first, then parquet
                try:
                    df = self.spark.read.format("delta").load(table_path)
                except:
                    df = self.spark.read.parquet(table_path)
            
            logger.info("Data loaded successfully", 
                       table_path=table_path, 
                       columns=len(df.columns))
            return df
            
        except Exception as e:
            logger.error("Failed to load data", table_path=table_path, error=str(e))
            return None
    
    def get_test_summary(self, test_suite: BronzeTestSuite) -> Dict[str, Any]:
        """
        Get a summary of test results
        
        Args:
            test_suite: Completed test suite
            
        Returns:
            Dictionary with test summary
        """
        summary = test_suite.get_summary()
        
        # Add quality dimension breakdown
        dimension_scores = {}
        for result in test_suite.test_results:
            for metric in result.metrics:
                if metric.dimension:
                    dim_name = metric.dimension.value
                    if dim_name not in dimension_scores:
                        dimension_scores[dim_name] = []
                    dimension_scores[dim_name].append(metric.value)
        
        # Calculate average scores per dimension
        dimension_averages = {}
        for dim, scores in dimension_scores.items():
            dimension_averages[dim] = sum(scores) / len(scores) if scores else 0.0
        
        summary['quality_dimensions'] = dimension_averages
        summary['recommendations'] = self._generate_recommendations(test_suite)
        
        return summary
    
    def _generate_recommendations(self, test_suite: BronzeTestSuite) -> List[str]:
        """Generate recommendations based on test results"""
        recommendations = []
        
        failed_tests = test_suite.get_failed_tests()
        warning_tests = test_suite.get_warning_tests()
        
        if failed_tests:
            recommendations.append("Address critical data quality issues before proceeding to Silver layer")
            for test in failed_tests:
                recommendations.append(f"Fix {test.test_name}: {test.error_message or 'Check test details'}")
        
        if warning_tests:
            recommendations.append("Review warning conditions for potential improvements")
            for test in warning_tests:
                for metric in test.metrics:
                    if metric.status == TestStatus.WARNING:
                        recommendations.append(f"Improve {metric.name}: {metric.message}")
        
        if test_suite.get_overall_score() < 0.9:
            recommendations.append("Overall quality score is below 90% - consider data source improvements")
        
        return recommendations
