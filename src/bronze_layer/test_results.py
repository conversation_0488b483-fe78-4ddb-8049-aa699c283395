"""
Test Results Data Models

This module defines the data structures for storing and managing
test results from the Bronze layer testing framework.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Any, Optional
from enum import Enum
import json


class TestStatus(Enum):
    """Test execution status"""
    PASS = "PASS"
    WARNING = "WARNING"
    FAIL = "FAIL"
    ERROR = "ERROR"
    SKIPPED = "SKIPPED"


class QualityDimension(Enum):
    """Data quality dimensions"""
    COMPLETENESS = "completeness"
    ACCURACY = "accuracy"
    CONSISTENCY = "consistency"
    VALIDITY = "validity"
    TIMELINESS = "timeliness"
    UNIQUENESS = "uniqueness"


@dataclass
class TestMetric:
    """Individual test metric result"""
    name: str
    value: float
    threshold: Optional[float] = None
    status: TestStatus = TestStatus.PASS
    message: str = ""
    dimension: Optional[QualityDimension] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "name": self.name,
            "value": self.value,
            "threshold": self.threshold,
            "status": self.status.value,
            "message": self.message,
            "dimension": self.dimension.value if self.dimension else None
        }


@dataclass
class TestResult:
    """Individual test result"""
    test_name: str
    status: TestStatus
    execution_time_seconds: float
    metrics: List[TestMetric] = field(default_factory=list)
    error_message: Optional[str] = None
    details: Dict[str, Any] = field(default_factory=dict)
    
    def add_metric(self, metric: TestMetric):
        """Add a metric to this test result"""
        self.metrics.append(metric)
    
    def get_overall_score(self) -> float:
        """Calculate overall quality score for this test"""
        if not self.metrics:
            return 1.0 if self.status == TestStatus.PASS else 0.0
        
        scores = [m.value for m in self.metrics if m.value is not None]
        return sum(scores) / len(scores) if scores else 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "test_name": self.test_name,
            "status": self.status.value,
            "execution_time_seconds": self.execution_time_seconds,
            "metrics": [m.to_dict() for m in self.metrics],
            "error_message": self.error_message,
            "details": self.details,
            "overall_score": self.get_overall_score()
        }


@dataclass
class BronzeTestSuite:
    """Complete Bronze layer test suite results"""
    table_path: str
    execution_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    overall_status: TestStatus = TestStatus.PASS
    test_results: List[TestResult] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_test_result(self, result: TestResult):
        """Add a test result to the suite"""
        self.test_results.append(result)
        self._update_overall_status()
    
    def _update_overall_status(self):
        """Update overall status based on individual test results"""
        if not self.test_results:
            return
        
        statuses = [r.status for r in self.test_results]
        
        if TestStatus.ERROR in statuses or TestStatus.FAIL in statuses:
            self.overall_status = TestStatus.FAIL
        elif TestStatus.WARNING in statuses:
            self.overall_status = TestStatus.WARNING
        else:
            self.overall_status = TestStatus.PASS
    
    def get_overall_score(self) -> float:
        """Calculate overall quality score for the entire suite"""
        if not self.test_results:
            return 0.0
        
        scores = [r.get_overall_score() for r in self.test_results]
        return sum(scores) / len(scores)
    
    def get_execution_time(self) -> float:
        """Get total execution time in seconds"""
        if self.end_time and self.start_time:
            return (self.end_time - self.start_time).total_seconds()
        return 0.0
    
    def get_failed_tests(self) -> List[TestResult]:
        """Get list of failed tests"""
        return [r for r in self.test_results 
                if r.status in [TestStatus.FAIL, TestStatus.ERROR]]
    
    def get_warning_tests(self) -> List[TestResult]:
        """Get list of tests with warnings"""
        return [r for r in self.test_results if r.status == TestStatus.WARNING]
    
    def get_summary(self) -> Dict[str, Any]:
        """Get summary statistics"""
        total_tests = len(self.test_results)
        passed = len([r for r in self.test_results if r.status == TestStatus.PASS])
        warnings = len(self.get_warning_tests())
        failed = len(self.get_failed_tests())
        skipped = len([r for r in self.test_results if r.status == TestStatus.SKIPPED])
        
        return {
            "total_tests": total_tests,
            "passed": passed,
            "warnings": warnings,
            "failed": failed,
            "skipped": skipped,
            "overall_score": self.get_overall_score(),
            "execution_time_seconds": self.get_execution_time(),
            "overall_status": self.overall_status.value
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "table_path": self.table_path,
            "execution_id": self.execution_id,
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "overall_status": self.overall_status.value,
            "test_results": [r.to_dict() for r in self.test_results],
            "metadata": self.metadata,
            "summary": self.get_summary()
        }
    
    def to_json(self) -> str:
        """Convert to JSON string"""
        return json.dumps(self.to_dict(), indent=2, default=str)
