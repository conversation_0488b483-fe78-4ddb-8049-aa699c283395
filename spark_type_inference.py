#!/usr/bin/env python3
"""
Enhanced Spark DataFrame Type Inference with Robust Timestamp Handling

This module provides improved type inference for Spark DataFrames with special
focus on handling various timestamp formats that commonly cause parsing errors.
"""

from pyspark.sql import DataFrame
from pyspark.sql.functions import col, to_timestamp, coalesce, when, trim, regexp_replace
import pandas as pd


def infer_nullable_column_type(series: pd.Series):
    """
    Infers the most appropriate data type for a nullable pandas Series.
    Enhanced to handle specific date formats like "02-Mar-25".
    
    Returns:
        inferred_dtype (str): 'int', 'float', 'bool', 'datetime', or 'string'
        converted_series (pd.Series): series with inferred type applied
    """
    if series.empty:
        return 'empty', series.astype('string')
    
    # Clean the series - replace empty strings and whitespace with NaN
    series = series.replace(r'^\s*$', pd.NA, regex=True)
    
    # Try numeric inference first
    numeric = pd.to_numeric(series, errors='coerce')
    if numeric.notna().any():
        if (numeric.dropna() == numeric.dropna().astype(int)).all():
            return 'int', numeric.astype('Int64')  # Nullable integer
        else:
            return 'float', numeric.astype('Float64')  # Nullable float
    
    # Try to infer datetime with enhanced format support
    try:
        # Common date formats to try, especially for formats like "02-Mar-25" and "12/2/2024 12:00:00 AM"
        date_formats = [
            # Handle AM/PM formats with single digit dates/months
            "%m/%d/%Y %I:%M:%S %p",    # 12/2/2024 12:00:00 AM
            "%m/%d/%Y %H:%M:%S",       # 12/2/2024 12:00:00
            "%d/%m/%Y %I:%M:%S %p",    # 2/12/2024 12:00:00 AM
            "%d/%m/%Y %H:%M:%S",       # 2/12/2024 12:00:00
            
            # Standard date formats
            "%d-%b-%y",      # 02-Mar-25
            "%d-%B-%y",      # 02-March-25  
            "%d-%b-%Y",      # 02-Mar-2025
            "%d-%B-%Y",      # 02-March-2025
            "%d/%m/%y",      # 02/03/25
            "%d/%m/%Y",      # 02/03/2025
            "%m/%d/%Y",      # 03/02/2025
            "%Y-%m-%d",      # 2025-03-02
            "%d.%m.%Y",      # 02.03.2025
            "%d-%m-%Y",      # 02-03-2025
            "%d-%m-%y",      # 02-03-25
            
            # Additional datetime formats
            "%Y-%m-%d %H:%M:%S",       # 2024-12-02 12:00:00
            "%Y-%m-%d %I:%M:%S %p",    # 2024-12-02 12:00:00 AM
            "%d-%m-%Y %H:%M:%S",       # 02-12-2024 12:00:00
            "%d-%m-%Y %I:%M:%S %p",    # 02-12-2024 12:00:00 AM
        ]
        
        # First try specific formats
        for fmt in date_formats:
            try:
                converted = pd.to_datetime(series, format=fmt, errors='raise')
                if converted.notna().any():
                    return 'datetime', converted
            except (ValueError, TypeError):
                continue
        
        # Fallback to automatic datetime inference with coercion for malformed data
        converted = pd.to_datetime(series, errors='coerce')
        if converted.notna().any():
            return 'datetime', converted
            
    except (ValueError, TypeError):
        pass
    
    # Try to infer boolean
    unique_vals = set(series.dropna().astype(str).str.lower())
    if unique_vals.issubset({'true', 'false'}):
        return 'bool', series.astype('bool')
    
    # Fallback to string
    return 'string', series.astype('string')


# Updated mapping for Spark compatibility
pandas_to_spark = {
    'Int64': 'int',
    'Float64': 'double',
    'bool': 'boolean',
    'boolean': 'boolean',
    'datetime64[ns]': 'timestamp',
    'string': 'string',
    'empty': 'string'
}


def cast_to_inferred_type(spark_df: DataFrame, debug: bool = True):
    """
    Cast Spark DataFrame columns to inferred types based on pandas analysis.
    Enhanced with robust timestamp handling to prevent parsing errors.
    
    Args:
        spark_df: Input Spark DataFrame
        debug: Whether to print debug information
        
    Returns:
        DataFrame with properly cast columns
    """
    data_type_mapping = dict()
    
    # Determine column types using pandas sample
    sample_pd = spark_df.limit(1000).toPandas()
    
    for column in sample_pd.columns:
        if debug:
            print(f"Analyzing column: {column}")
            print(f"Sample values: {sample_pd[column].head()}")
        
        column_type, converted_series = infer_nullable_column_type(sample_pd[column])
        data_type_mapping[column] = converted_series.dtype.name
        
        if debug:
            print(f"Inferred type: {column_type}")
            print(f"Pandas dtype: {converted_series.dtype}")
            print("---")
    
    # Cast the columns to the correct data type in Spark
    for column in spark_df.columns:
        escaped_col = f"`{column}`" if "." in column else column
        pandas_dtype = data_type_mapping[column]
        spark_dtype = pandas_to_spark.get(pandas_dtype, 'string')
        
        if debug:
            print(f"Casting {column}: {pandas_dtype} -> {spark_dtype}")
        
        # Special handling for timestamp columns to avoid casting errors
        if spark_dtype == 'timestamp':
            spark_df = cast_timestamp_column_robust(spark_df, column, escaped_col, debug)
        else:
            # For other data types, use regular casting
            spark_df = spark_df.withColumn(column, col(escaped_col).cast(spark_dtype))
    
    return spark_df


def cast_timestamp_column_robust(spark_df: DataFrame, column: str, escaped_col: str, debug: bool = True):
    """
    Robust timestamp casting with multiple format attempts and error handling.
    
    Args:
        spark_df: Input DataFrame
        column: Column name
        escaped_col: Escaped column name for Spark
        debug: Whether to print debug info
        
    Returns:
        DataFrame with timestamp column cast
    """
    if debug:
        print(f"Applying robust timestamp casting to column: {column}")
    
    # First, clean the column - trim whitespace and handle empty values
    cleaned_col = trim(col(escaped_col))
    
    # Apply multiple timestamp parsing attempts with coalesce for fallback
    timestamp_col = coalesce(
        # Most common formats first (most specific to least specific)
        to_timestamp(cleaned_col, "M/d/yyyy h:mm:ss a"),      # 12/2/2024 12:00:00 AM
        to_timestamp(cleaned_col, "M/d/yyyy H:mm:ss"),        # 12/2/2024 12:00:00
        to_timestamp(cleaned_col, "yyyy-MM-dd HH:mm:ss"),     # 2024-12-02 12:00:00
        to_timestamp(cleaned_col, "yyyy-MM-dd"),              # 2024-12-02
        
        # Date formats without time - handle the problematic formats
        to_timestamp(cleaned_col, "dd-MMM-yy"),               # 05-Jun-24 (your problematic format)
        to_timestamp(cleaned_col, "dd-MMM-yyyy"),             # 05-Jun-2024
        to_timestamp(cleaned_col, "d-MMM-yy"),                # 5-Jun-24 (single digit day)
        to_timestamp(cleaned_col, "d-MMM-yyyy"),              # 5-Jun-2024
        
        # Additional common formats
        to_timestamp(cleaned_col, "dd/MM/yyyy"),              # 05/06/2024
        to_timestamp(cleaned_col, "MM/dd/yyyy"),              # 06/05/2024
        to_timestamp(cleaned_col, "d/M/yyyy"),                # 5/6/2024
        to_timestamp(cleaned_col, "M/d/yyyy"),                # 6/5/2024
        to_timestamp(cleaned_col, "dd-MM-yyyy"),              # 05-06-2024
        to_timestamp(cleaned_col, "MM-dd-yyyy"),              # 06-05-2024
        to_timestamp(cleaned_col, "yyyy/MM/dd"),              # 2024/06/05
        
        # Fallback to default parser (most permissive, but may fail)
        to_timestamp(cleaned_col)
    )
    
    # Apply the timestamp conversion
    spark_df = spark_df.withColumn(column, timestamp_col)
    
    if debug:
        print(f"Timestamp casting applied to column: {column}")
    
    return spark_df


def validate_timestamp_conversion(spark_df: DataFrame, timestamp_columns: list = None):
    """
    Validate that timestamp conversion was successful by checking for null values.
    
    Args:
        spark_df: DataFrame after timestamp conversion
        timestamp_columns: List of timestamp column names to check
        
    Returns:
        Dict with validation results
    """
    if timestamp_columns is None:
        # Auto-detect timestamp columns
        timestamp_columns = [field.name for field in spark_df.schema.fields 
                           if str(field.dataType) == 'TimestampType']
    
    validation_results = {}
    
    for col_name in timestamp_columns:
        total_count = spark_df.count()
        null_count = spark_df.filter(col(col_name).isNull()).count()
        success_rate = (total_count - null_count) / total_count if total_count > 0 else 0
        
        validation_results[col_name] = {
            'total_rows': total_count,
            'null_rows': null_count,
            'success_rate': success_rate,
            'status': 'PASS' if success_rate > 0.95 else 'WARNING' if success_rate > 0.8 else 'FAIL'
        }
        
        print(f"Timestamp validation for {col_name}:")
        print(f"  Total rows: {total_count}")
        print(f"  Null rows: {null_count}")
        print(f"  Success rate: {success_rate:.2%}")
        print(f"  Status: {validation_results[col_name]['status']}")
        print()
    
    return validation_results


# Example usage function
def example_usage():
    """Example of how to use the enhanced type inference"""
    from pyspark.sql import SparkSession
    
    # Initialize Spark
    spark = SparkSession.builder.appName("TypeInferenceExample").getOrCreate()
    
    # Sample data with problematic timestamp formats
    sample_data = [
        ("1", "05-Jun-24", "100.50", "true"),
        ("2", "12/2/2024 12:00:00 AM", "200.75", "false"),
        ("3", "2024-12-03", "300.25", "true"),
        ("4", "invalid_date", "400.00", "false")
    ]
    
    columns = ["id", "date_col", "amount", "flag"]
    df = spark.createDataFrame(sample_data, columns)
    
    print("Original DataFrame:")
    df.show()
    df.printSchema()
    
    # Apply type inference
    typed_df = cast_to_inferred_type(df, debug=True)
    
    print("\nAfter type inference:")
    typed_df.show()
    typed_df.printSchema()
    
    # Validate timestamp conversion
    validation_results = validate_timestamp_conversion(typed_df)
    
    return typed_df, validation_results


if __name__ == "__main__":
    example_usage()
