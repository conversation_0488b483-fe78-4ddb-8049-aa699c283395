# Test Configuration for Data Testing Framework
# Simplified configuration for testing without external dependencies

# Environment settings
environment:
  name: "test"
  catalog_name: "test_catalog"
  schema_name: "bronze"

# Bronze layer testing configuration
bronze_layer:
  # Test configuration
  tests:
    file_integrity: true
    schema_validation: true
    record_counting: true
    timestamp_validation: true
    metadata_preservation: true
    duplicate_detection: true
  
  # Quality thresholds
  thresholds:
    completeness_min: 0.95      # 95% completeness required
    accuracy_min: 0.98          # 98% accuracy required
    consistency_min: 0.95       # 95% consistency required
    validity_min: 0.98          # 98% validity required
    timeliness_hours: 24        # Data must be within 24 hours
    uniqueness_min: 0.98        # 98% uniqueness required
    duplicate_threshold: 0.05   # Max 5% duplicates allowed
  
  # File processing settings
  file_processing:
    max_file_size_gb: 10
    supported_formats: ["parquet", "delta", "json", "csv"]
    encoding: "utf-8"
    compression: "snappy"

# Quality gate configuration
quality_gates:
  # Decision thresholds
  pass_threshold: 0.95          # Overall score >= 95% = PASS
  warning_threshold: 0.85       # Overall score >= 85% = WARNING
  
  # Quarantine settings
  quarantine:
    enabled: true
    storage_path: "/tmp/quarantine"
    retention_days: 30
    auto_quarantine_threshold: 0.7
  
  # Escalation settings
  escalation:
    emergency_contact_threshold: 0.5
    auto_recovery_enabled: false
    max_retry_attempts: 3

# Notification configuration (disabled for testing)
notifications:
  # Email notifications (disabled for testing)
  email:
    enabled: false
    smtp_server: "localhost"
    smtp_port: 587
    smtp_username: ""
    smtp_password: ""
    from_address: "<EMAIL>"
    
    # Recipient configuration
    recipients:
      critical: ["<EMAIL>"]
      warning: ["<EMAIL>"]
      info: ["<EMAIL>"]
      
  # Slack notifications (disabled)
  slack:
    enabled: false
    webhook_url: ""
    channel: "#data-quality"
    username: "DataQualityBot"
    include_details: false
    include_charts: false
    mention_on_critical: false
    critical_mentions: []
    
  # Webhook notifications (disabled)
  webhooks:
    enabled: false
    endpoints: []

# Logging configuration
logging:
  level: "INFO"
  format: "console"
  
  # Log destinations
  destinations:
    console: true
    file: false
    databricks: false
  
  # File logging (disabled for testing)
  file_logging:
    enabled: false
    log_file: "/tmp/data_quality.log"
    max_size_mb: 100
    backup_count: 5
  
  # Databricks logging (disabled for testing)
  databricks_logging:
    enabled: false
    log_table: "logs.data_quality_logs"

# Performance settings
performance:
  # Spark configuration
  spark:
    executor_memory: "2g"
    executor_cores: 2
    max_result_size: "1g"
    sql_adaptive_enabled: true
    sql_adaptive_coalesce_partitions_enabled: true
  
  # Processing limits
  processing:
    max_records_per_batch: 1000000
    sample_size_for_profiling: 10000
    timeout_seconds: 3600

# Security settings (minimal for testing)
security:
  encryption:
    enabled: false
  
  access_control:
    enabled: false
