# Medallion Architecture Data Testing Framework Configuration Template
# Copy this file to config.yaml and customize for your environment

# Environment settings
environment:
  name: "development"  # development, staging, production
  databricks_workspace_url: "https://your-workspace.cloud.databricks.com"
  catalog_name: "your_catalog"
  schema_name: "your_schema"

# Bronze layer testing configuration
bronze_layer:
  # Test enablement flags
  tests:
    file_integrity: true
    schema_validation: true
    record_counting: true
    timestamp_validation: true
    metadata_preservation: true
    duplicate_detection: true
    
  # Quality thresholds (0.0 to 1.0)
  thresholds:
    completeness_min: 0.95      # Minimum percentage of complete records
    accuracy_min: 0.99          # Minimum accuracy threshold
    consistency_min: 0.98       # Minimum consistency threshold
    validity_min: 0.97          # Minimum validity threshold
    timeliness_max_hours: 24    # Maximum acceptable data age in hours
    uniqueness_min: 0.99        # Minimum uniqueness threshold
    
  # File processing settings
  file_processing:
    max_file_size_gb: 10        # Maximum file size in GB
    supported_formats: ["parquet", "delta", "json", "csv"]
    encoding: "utf-8"
    
  # Schema validation settings
  schema_validation:
    strict_mode: false          # Fail on any schema changes
    allow_new_columns: true     # Allow new columns in data
    allow_missing_columns: false # Allow missing expected columns
    
# Quality gates configuration
quality_gates:
  # Decision thresholds
  pass_threshold: 0.95          # Above this = PASS
  warning_threshold: 0.85       # Between warning and pass = WARNING
  # Below warning_threshold = FAIL
  
  # Escalation settings
  escalation:
    auto_recovery_attempts: 3
    escalation_delay_minutes: 15
    emergency_contact_threshold: 0.50  # Below this triggers emergency contact
    
  # Quarantine settings
  quarantine:
    enabled: true
    retention_days: 30
    auto_review_enabled: true
    max_quarantine_size_gb: 100

# Notification configuration
notifications:
  # Email notifications
  email:
    enabled: true
    smtp_server: "smtp.your-company.com"
    smtp_port: 587
    smtp_username: "<EMAIL>"
    smtp_password: "${EMAIL_PASSWORD}"  # Use environment variable
    from_address: "<EMAIL>"
    
    # Recipient configuration
    recipients:
      critical: ["<EMAIL>", "<EMAIL>"]
      warning: ["<EMAIL>"]
      info: ["<EMAIL>"]
      
  # Slack notifications
  slack:
    enabled: true
    webhook_url: "${SLACK_WEBHOOK_URL}"  # Use environment variable
    channel: "#data-quality"
    username: "DataQualityBot"
    
    # Message configuration
    include_details: true
    include_charts: false
    mention_on_critical: true
    critical_mentions: ["@data-team", "@ops-team"]
    
  # Webhook notifications (for custom integrations)
  webhooks:
    enabled: false
    endpoints:
      - name: "custom_system"
        url: "https://your-system.com/webhook"
        headers:
          Authorization: "Bearer ${WEBHOOK_TOKEN}"
        retry_attempts: 3
        
# Logging configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  format: "json"  # json, text
  
  # Log destinations
  destinations:
    console: true
    file: true
    databricks: true
    
  # File logging settings
  file_logging:
    path: "/tmp/data_quality_logs"
    max_size_mb: 100
    backup_count: 5
    
  # Databricks logging
  databricks_logging:
    log_table: "data_quality_logs"
    batch_size: 1000
    
# Monitoring and metrics
monitoring:
  # Prometheus metrics
  prometheus:
    enabled: false
    port: 8000
    metrics_prefix: "data_quality"
    
  # Custom metrics
  custom_metrics:
    enabled: true
    storage_table: "data_quality_metrics"
    retention_days: 90
    
# Performance settings
performance:
  # Spark configuration
  spark:
    executor_memory: "4g"
    executor_cores: 2
    max_executors: 10
    
  # Processing limits
  processing:
    max_concurrent_tests: 5
    timeout_minutes: 60
    memory_limit_gb: 16
    
# Security settings
security:
  # Data masking for sensitive fields
  data_masking:
    enabled: true
    sensitive_columns: ["ssn", "credit_card", "email"]
    masking_method: "hash"  # hash, mask, encrypt
    
  # Access control
  access_control:
    require_authentication: true
    allowed_users: ["<EMAIL>"]
    admin_users: ["<EMAIL>"]
