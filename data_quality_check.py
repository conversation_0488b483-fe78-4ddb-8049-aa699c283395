#!/usr/bin/env python3
"""
Data Quality Check Script

Works both locally and in Databricks workflows.
Automatically detects environment and adapts accordingly.

Local Usage:
    python data_quality_check.py --table-path /path/to/table --expected-count 1000
    python data_quality_check.py --test-scenario good

Databricks Usage:
    # Set parameters in workflow or notebook
    dbutils.widgets.text("table_path", "main.bronze.your_table")
    dbutils.widgets.text("expected_count", "10000")
    %run ./data_quality_check
"""

import os
import sys
import json
import yaml
import argparse
import tempfile
import shutil
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from pathlib import Path

# Environment detection
try:
    import dbutils
    DATABRICKS_ENV = True
except ImportError:
    DATABRICKS_ENV = False
    # Mock dbutils for local testing
    class MockDbutils:
        class widgets:
            _widgets = {}
            @classmethod
            def text(cls, name, default=""): cls._widgets[name] = default
            @classmethod
            def get(cls, name, default=""): return cls._widgets.get(name, default)
        class notebook:
            @staticmethod
            def exit(value):
                print(f"Exit: {json.dumps(value, indent=2) if isinstance(value, dict) else value}")
                sys.exit(0 if (isinstance(value, dict) and value.get('status') == 'success') else 1)
        class secrets:
            @staticmethod
            def get(scope, key): return f"mock_{scope}_{key}"
    dbutils = MockDbutils()

# Add src to path
script_dir = Path(__file__).parent
src_dir = script_dir / "src"
if src_dir.exists():
    sys.path.insert(0, str(src_dir))

# Imports
try:
    from pyspark.sql import SparkSession
    from pyspark.sql.types import StructType, StructField, StringType, IntegerType, DoubleType, TimestampType
    from pyspark.sql.functions import col, lit, when, rand
    from src.data_testing_framework import DataTestingFramework
    from src.utils.logging_config import configure_logging
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Install dependencies: pip install pyspark delta-spark pydantic structlog jinja2")
    sys.exit(1)


def get_parameters():
    """Get parameters from command line or Databricks widgets"""
    if DATABRICKS_ENV:
        return {
            "table_path": dbutils.widgets.get("table_path", ""),
            "expected_count": int(dbutils.widgets.get("expected_count", "0")) or None,
            "test_scenario": dbutils.widgets.get("test_scenario", ""),
            "batch_id": dbutils.widgets.get("batch_id", f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}"),
            "source_system": dbutils.widgets.get("source_system", "unknown"),
            "fail_on_warning": dbutils.widgets.get("fail_on_warning", "false").lower() == "true",
            "send_notifications": dbutils.widgets.get("send_notifications", "false").lower() == "true"
        }
    else:
        parser = argparse.ArgumentParser(description="Data Quality Check")
        parser.add_argument("--table-path", help="Path to Bronze table")
        parser.add_argument("--expected-count", type=int, help="Expected record count")
        parser.add_argument("--test-scenario", choices=["good", "poor", "bad"], help="Test with sample data")
        parser.add_argument("--batch-id", default=f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        parser.add_argument("--source-system", default="test_system")
        parser.add_argument("--fail-on-warning", action="store_true")
        parser.add_argument("--send-notifications", action="store_true")
        args = parser.parse_args()
        
        return {
            "table_path": args.table_path or "",
            "expected_count": args.expected_count,
            "test_scenario": args.test_scenario or "",
            "batch_id": args.batch_id,
            "source_system": args.source_system,
            "fail_on_warning": args.fail_on_warning,
            "send_notifications": args.send_notifications
        }


def create_config(params):
    """Create configuration for both environments"""
    return {
        "environment": {
            "name": "production" if DATABRICKS_ENV else "local_test",
            "catalog_name": "main" if DATABRICKS_ENV else "test_catalog",
            "schema_name": "bronze"
        },
        "bronze_layer": {
            "tests": {
                "file_integrity": True,
                "schema_validation": True,
                "record_counting": True,
                "timestamp_validation": True,
                "metadata_preservation": True,
                "duplicate_detection": True
            },
            "thresholds": {
                "completeness_min": 0.95,
                "accuracy_min": 0.98,
                "consistency_min": 0.95,
                "validity_min": 0.98,
                "timeliness_hours": 24,
                "uniqueness_min": 0.98,
                "duplicate_threshold": 0.05
            }
        },
        "quality_gates": {
            "pass_threshold": 0.95,
            "warning_threshold": 0.85,
            "quarantine": {
                "enabled": True,
                "storage_path": "/mnt/quarantine/bronze" if DATABRICKS_ENV else "/tmp/quarantine",
                "retention_days": 30
            }
        },
        "notifications": {
            "email": {
                "enabled": params["send_notifications"],
                "smtp_server": "smtp.company.com" if DATABRICKS_ENV else "localhost",
                "smtp_port": 587,
                "from_address": "<EMAIL>",
                "recipients": {
                    "critical": ["<EMAIL>"],
                    "warning": ["<EMAIL>"],
                    "info": ["<EMAIL>"]
                }
            },
            "slack": {"enabled": False}
        },
        "logging": {
            "level": "INFO",
            "destinations": {
                "console": True,
                "databricks": DATABRICKS_ENV
            }
        }
    }


def create_sample_data(scenario, spark):
    """Create sample data for testing (local only)"""
    if DATABRICKS_ENV:
        raise ValueError("Sample data generation not available in Databricks")
    
    print(f"📊 Generating {scenario} quality sample data...")
    
    # Base schema
    schema = StructType([
        StructField("order_id", StringType(), False),
        StructField("customer_id", StringType(), False),
        StructField("product_id", StringType(), False),
        StructField("quantity", IntegerType(), True),
        StructField("unit_price", DoubleType(), True),
        StructField("total_amount", DoubleType(), True),
        StructField("order_date", StringType(), True),
        StructField("customer_email", StringType(), True),
        StructField("ingestion_timestamp", TimestampType(), False),
        StructField("batch_id", StringType(), True)
    ])
    
    # Create base data
    num_records = 1000
    base_time = datetime.now() - timedelta(minutes=30)
    
    data = []
    for i in range(num_records):
        data.append({
            "order_id": f"ORD-{i+1:06d}",
            "customer_id": f"CUST-{(i % 100) + 1:04d}",
            "product_id": f"PROD-{(i % 50) + 1:03d}",
            "quantity": (i % 5) + 1,
            "unit_price": round(10.0 + (i % 100), 2),
            "total_amount": round((10.0 + (i % 100)) * ((i % 5) + 1), 2),
            "order_date": (base_time - timedelta(days=i % 7)).strftime("%Y-%m-%d"),
            "customer_email": f"customer{(i % 100) + 1}@email.com",
            "ingestion_timestamp": base_time + timedelta(seconds=i),
            "batch_id": "BATCH-001"
        })
    
    df = spark.createDataFrame(data, schema)
    
    # Apply quality issues based on scenario
    if scenario == "poor":
        # Introduce moderate issues
        df = df.withColumn("customer_email", when(rand() < 0.1, None).otherwise(col("customer_email")))
        df = df.withColumn("quantity", when(rand() < 0.02, -1).otherwise(col("quantity")))
        old_timestamp = datetime.now() - timedelta(days=2)
        df = df.withColumn("ingestion_timestamp", when(rand() < 0.1, lit(old_timestamp)).otherwise(col("ingestion_timestamp")))
        
    elif scenario == "bad":
        # Introduce severe issues
        df = df.withColumn("order_id", when(rand() < 0.5, None).otherwise(col("order_id")))
        df = df.withColumn("customer_id", when(rand() < 0.3, None).otherwise(col("customer_id")))
        df = df.withColumn("quantity", when(rand() < 0.2, -999).otherwise(col("quantity")))
        very_old_timestamp = datetime.now() - timedelta(days=30)
        df = df.withColumn("ingestion_timestamp", lit(very_old_timestamp))
    
    # Save to temp location (use parquet for local testing, delta for Databricks)
    temp_dir = tempfile.mkdtemp()
    table_path = os.path.join(temp_dir, "test_table")
    format_type = "delta" if DATABRICKS_ENV else "parquet"
    df.write.format(format_type).mode("overwrite").save(table_path)
    
    print(f"💾 Sample data saved to: {table_path}")
    return table_path, temp_dir, num_records


def determine_pipeline_action(results, params):
    """Determine what the pipeline should do"""
    decision = results['quality_gate']['decision']
    
    if decision == "PASS":
        return {
            "status": "success",
            "action": "proceed",
            "message": "✅ Data quality PASSED - proceed to Silver layer",
            "continue_pipeline": True,
            "exit_code": 0
        }
    elif decision == "WARNING":
        if params["fail_on_warning"]:
            return {
                "status": "warning_blocked",
                "action": "stop",
                "message": "⚠️ Data quality WARNING - pipeline blocked",
                "continue_pipeline": False,
                "exit_code": 1
            }
        else:
            return {
                "status": "warning_proceed",
                "action": "proceed_with_caution",
                "message": "⚠️ Data quality WARNING - proceeding with caution",
                "continue_pipeline": True,
                "exit_code": 0
            }
    elif decision in ["FAIL", "QUARANTINE"]:
        return {
            "status": "failed",
            "action": "stop",
            "message": f"❌ Data quality FAILED ({decision}) - pipeline blocked",
            "continue_pipeline": False,
            "exit_code": 1
        }
    elif decision == "EMERGENCY":
        return {
            "status": "emergency",
            "action": "halt_all",
            "message": "🚨 EMERGENCY - all processing halted",
            "continue_pipeline": False,
            "exit_code": 2
        }


def display_results(results, params):
    """Display test results"""
    print("\n" + "=" * 60)
    print("📊 DATA QUALITY RESULTS")
    print("=" * 60)
    
    quality_gate = results['quality_gate']
    decision_emoji = {"PASS": "✅", "WARNING": "⚠️", "FAIL": "❌", "QUARANTINE": "🔒", "EMERGENCY": "🚨"}
    emoji = decision_emoji.get(quality_gate['decision'], '📊')
    
    print(f"🆔 Execution ID: {results['execution_id']}")
    print(f"📁 Table: {params['table_path']}")
    print(f"⏱️  Duration: {results['execution_time_seconds']:.2f}s")
    print(f"\n{emoji} Decision: {quality_gate['decision']}")
    print(f"📈 Overall Score: {quality_gate['overall_score']:.2%}")
    
    # Test summary
    test_summary = results['test_summary']
    print(f"\n📋 Tests: {test_summary['passed']}/{test_summary['total_tests']} passed")
    if test_summary['warnings'] > 0:
        print(f"⚠️  Warnings: {test_summary['warnings']}")
    if test_summary['failed'] > 0:
        print(f"❌ Failed: {test_summary['failed']}")
    
    # Individual test results
    print(f"\n🔍 Test Details:")
    for test_result in results['test_suite']['test_results']:
        status_emoji = {"PASS": "✅", "WARNING": "⚠️", "FAIL": "❌", "ERROR": "💥"}
        emoji = status_emoji.get(test_result['status'], '❓')
        score = test_result.get('overall_score', 0)
        print(f"   {emoji} {test_result['test_name']}: {test_result['status']} ({score:.1%})")


def main():
    """Main function"""
    temp_dir = None
    
    try:
        # Get parameters
        params = get_parameters()
        
        # Validate
        if not params["table_path"] and not params["test_scenario"]:
            error_msg = "Either table_path or test_scenario must be specified"
            if DATABRICKS_ENV:
                dbutils.notebook.exit({"status": "error", "message": error_msg, "exit_code": 1})
            else:
                print(f"❌ {error_msg}")
                sys.exit(1)
        
        # Configure logging
        configure_logging({"level": "INFO", "destinations": {"console": True, "databricks": DATABRICKS_ENV}})
        
        # Initialize Spark
        if DATABRICKS_ENV:
            spark = SparkSession.getActiveSession() or SparkSession.builder.appName("DataQualityCheck").getOrCreate()
        else:
            print("⚡ Initializing Spark...")
            # Simple Spark session for local testing (no Delta Lake for compatibility)
            spark = SparkSession.builder \
                .appName("DataQualityCheck") \
                .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer") \
                .config("spark.sql.adaptive.enabled", "true") \
                .config("spark.sql.adaptive.coalescePartitions.enabled", "true") \
                .getOrCreate()
        
        # Handle sample data (local only)
        if params["test_scenario"]:
            if DATABRICKS_ENV:
                dbutils.notebook.exit({"status": "error", "message": "Sample data not supported in Databricks", "exit_code": 1})
            
            table_path, temp_dir, record_count = create_sample_data(params["test_scenario"], spark)
            params["table_path"] = table_path
            params["expected_count"] = record_count
        
        # Log execution
        print(f"\n🚀 DATA QUALITY CHECK ({'Databricks' if DATABRICKS_ENV else 'Local'})")
        print(f"📊 Table: {params['table_path']}")
        print(f"🔢 Expected: {params['expected_count'] or 'Not specified'}")
        print(f"📦 Batch: {params['batch_id']}")
        
        # Initialize framework
        config = create_config(params)

        # Create temporary config file for framework
        temp_config_path = os.path.join(temp_dir, "temp_config.yaml")
        with open(temp_config_path, 'w') as f:
            yaml.dump(config, f)

        framework = DataTestingFramework(config_path=temp_config_path, spark=spark)
        
        # Validate config
        validation = framework.validate_configuration()
        if not validation["config_valid"]:
            error_msg = f"Config validation failed: {validation['issues']}"
            if DATABRICKS_ENV:
                dbutils.notebook.exit({"status": "config_error", "message": error_msg, "exit_code": 1})
            else:
                print(f"❌ {error_msg}")
                sys.exit(1)
        
        # Run tests
        print("🔍 Running data quality tests...")
        source_metadata = {
            "source_system": params["source_system"],
            "batch_id": params["batch_id"],
            "extraction_timestamp": datetime.now().isoformat(),
            "environment": "databricks" if DATABRICKS_ENV else "local"
        }
        
        results = framework.run_bronze_pipeline_tests(
            table_path=params["table_path"],
            expected_count=params["expected_count"],
            source_metadata=source_metadata,
            timestamp_column="ingestion_timestamp",
            send_notifications=params["send_notifications"]
        )
        
        # Display results
        display_results(results, params)
        
        # Determine action
        action = determine_pipeline_action(results, params)
        
        print(f"\n🚦 PIPELINE DECISION: {action['action']}")
        print(f"💬 {action['message']}")
        
        # Exit
        exit_data = {
            "status": action["status"],
            "action": action["action"],
            "message": action["message"],
            "continue_pipeline": action["continue_pipeline"],
            "quality_decision": results['quality_gate']['decision'],
            "overall_score": results['quality_gate']['overall_score'],
            "execution_id": results['execution_id'],
            "exit_code": action["exit_code"]
        }
        
        if DATABRICKS_ENV:
            dbutils.notebook.exit(exit_data)
        else:
            print(f"\n📋 Exit: {json.dumps(exit_data, indent=2)}")
            sys.exit(action["exit_code"])
        
    except Exception as e:
        error_msg = f"Data quality check failed: {str(e)}"
        print(f"❌ {error_msg}")
        
        if DATABRICKS_ENV:
            dbutils.notebook.exit({"status": "error", "message": error_msg, "exit_code": 1})
        else:
            import traceback
            traceback.print_exc()
            sys.exit(1)
    
    finally:
        # Cleanup
        if temp_dir and os.path.exists(temp_dir):
            shutil.rmtree(temp_dir, ignore_errors=True)
        if not DATABRICKS_ENV and 'spark' in locals():
            spark.stop()


if __name__ == "__main__":
    main()
