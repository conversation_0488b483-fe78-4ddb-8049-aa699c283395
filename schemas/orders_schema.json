{"type": "struct", "fields": [{"name": "order_id", "type": "string", "nullable": false, "metadata": {}}, {"name": "customer_id", "type": "string", "nullable": false, "metadata": {}}, {"name": "product_id", "type": "string", "nullable": false, "metadata": {}}, {"name": "quantity", "type": "integer", "nullable": false, "metadata": {}}, {"name": "unit_price", "type": "double", "nullable": false, "metadata": {}}, {"name": "total_amount", "type": "double", "nullable": false, "metadata": {}}, {"name": "order_date", "type": "timestamp", "nullable": false, "metadata": {}}, {"name": "customer_email", "type": "string", "nullable": true, "metadata": {}}, {"name": "product_category", "type": "string", "nullable": true, "metadata": {}}, {"name": "ingestion_timestamp", "type": "timestamp", "nullable": false, "metadata": {}}]}