{"type": "struct", "fields": [{"name": "customer_id", "type": "string", "nullable": false, "metadata": {}}, {"name": "first_name", "type": "string", "nullable": false, "metadata": {}}, {"name": "last_name", "type": "string", "nullable": false, "metadata": {}}, {"name": "email", "type": "string", "nullable": false, "metadata": {}}, {"name": "phone", "type": "string", "nullable": true, "metadata": {}}, {"name": "registration_date", "type": "timestamp", "nullable": false, "metadata": {}}, {"name": "ingestion_timestamp", "type": "timestamp", "nullable": false, "metadata": {}}]}