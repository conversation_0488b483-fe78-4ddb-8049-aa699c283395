# Medallion Architecture Data Testing Framework

A comprehensive data quality testing framework for Databricks medallion architecture (Bronze, Silver, Gold layers) that ensures data integrity and reliability throughout your data pipeline.

## Overview

This framework implements the data quality strategy outlined in your organization's data quality document, providing:

- **Bronze Layer Testing**: File integrity, schema validation, record counting, and metadata preservation
- **Quality Gate Engine**: Automated Pass/Warning/Fail decisions with configurable thresholds
- **Quarantine System**: Safe isolation of data that doesn't meet quality standards
- **Notification System**: Real-time alerts via email, Slack, and webhooks
- **Comprehensive Logging**: Full audit trail and monitoring capabilities

## Architecture

```
mbcl-data-testing/
├── src/
│   ├── bronze_layer/           # Bronze layer specific tests
│   ├── quality_gates/          # Quality gate engine
│   ├── notifications/          # Notification system
│   ├── quarantine/            # Data quarantine management
│   ├── config/                # Configuration management
│   └── utils/                 # Shared utilities
├── tests/                     # Unit and integration tests
├── config/                    # Configuration files
├── examples/                  # Usage examples
├── docs/                      # Documentation
└── requirements.txt           # Python dependencies
```

## Quick Start

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure Your Environment**
   ```bash
   cp config/config.template.yaml config/config.yaml
   # Edit config.yaml with your settings
   ```

3. **Run Bronze Layer Tests**
   ```python
   from src.data_testing_framework import DataTestingFramework
   from src.utils.logging_config import configure_logging

   # Configure logging
   configure_logging({"level": "INFO", "format": "console"})

   # Initialize framework
   framework = DataTestingFramework("config/config.yaml")

   # Validate configuration
   validation = framework.validate_configuration()
   if not validation["config_valid"]:
       print("Configuration issues found!")
       return

   # Run tests
   results = framework.run_bronze_pipeline_tests(
       table_path="/path/to/bronze/table",
       expected_count=10000,
       send_notifications=True
   )

   print(f"Quality Decision: {results['quality_gate']['decision']}")
   print(f"Overall Score: {results['quality_gate']['overall_score']:.2%}")

   # Check if pipeline should continue
   if results['quality_gate']['decision'] in ['PASS', 'WARNING']:
       print("✅ Pipeline can proceed to Silver layer")
   else:
       print("❌ Pipeline blocked - fix issues before proceeding")
   ```

## Detailed Usage Guide

### 1. Framework Initialization

```python
from src.data_testing_framework import DataTestingFramework

# Option 1: Initialize with config file
framework = DataTestingFramework(config_path="config/config.yaml")

# Option 2: Initialize with existing Spark session
framework = DataTestingFramework(config_path="config/config.yaml", spark=spark)

# Option 3: Initialize with config dictionary (for Databricks)
config_dict = {...}  # Your configuration
framework = DataTestingFramework(config_dict=config_dict)
```

### 2. Running Bronze Layer Tests

The main method `run_bronze_pipeline_tests()` orchestrates the complete testing workflow:

```python
results = framework.run_bronze_pipeline_tests(
    table_path="/path/to/bronze/table",           # Required: Path to Bronze table
    expected_schema=expected_schema,              # Optional: Expected schema
    expected_count=10000,                         # Optional: Expected record count
    source_metadata=source_metadata,              # Optional: Source system metadata
    timestamp_column="ingestion_timestamp",       # Optional: Timestamp column name
    send_notifications=True                       # Optional: Send notifications
)
```

### 3. Understanding Test Results

The framework returns a comprehensive results dictionary:

```python
{
    "execution_id": "uuid-string",
    "table_path": "/path/to/table",
    "quality_gate": {
        "decision": "WARNING",           # PASS, WARNING, FAIL, QUARANTINE, EMERGENCY
        "overall_score": 0.92,          # 0.0 to 1.0
        "threshold_met": true,
        "quarantine_required": false,
        "emergency_escalation": false,
        "reasons": [...],               # Why this decision was made
        "recommendations": [...]        # What to do next
    },
    "test_summary": {
        "total_tests": 5,
        "passed": 4,
        "warnings": 1,
        "failed": 0,
        "errors": 0
    },
    "notifications": {
        "email": true,                  # Whether notifications were sent
        "slack": true,
        "webhooks": false
    },
    "next_actions": [...]              # Recommended next steps
}
```

### 4. Quality Gate Decisions

The framework makes one of five decisions:

- **PASS** (✅): Data quality meets all standards, proceed to Silver layer
- **WARNING** (⚠️): Data quality acceptable but monitor closely
- **FAIL** (❌): Data quality below standards, do not proceed
- **QUARANTINE** (🔒): Data has critical issues, move to quarantine storage
- **EMERGENCY** (🚨): Severe issues detected, escalate immediately

## Features

### Bronze Layer Testing
- **File Integrity**: Verify files weren't corrupted during transfer
- **Schema Validation**: Ensure data structure matches expectations
- **Record Counting**: Verify all expected records were received
- **Timestamp Validation**: Check data arrival times are logical
- **Metadata Preservation**: Track all source information
- **Duplicate Detection**: Identify and measure duplicate records

### Quality Gates
- **Configurable Thresholds**: Set Pass/Warning/Fail criteria
- **Automatic Decision Making**: Intelligent routing based on quality scores
- **Escalation Procedures**: Automated recovery and alert notifications
- **Quarantine System**: Isolate bad data for investigation

### Notification System
- **Multiple Channels**: Email, Slack, webhooks, and custom integrations
- **Severity-Based Routing**: Different notifications for different issue types
- **Rich Context**: Detailed information about quality issues and remediation steps
- **Test Notifications**: Verify configuration with test messages

## Examples

### Basic Usage
See `examples/basic_usage.py` for a complete standalone example.

### Databricks Integration
See `examples/databricks_notebook_example.py` for a Databricks notebook implementation.

### Pipeline Integration
```python
# In your ETL pipeline (after Bronze layer processing)
from src.data_testing_framework import DataTestingFramework

def run_data_quality_tests(table_path, expected_count):
    framework = DataTestingFramework("config/config.yaml")

    results = framework.run_bronze_pipeline_tests(
        table_path=table_path,
        expected_count=expected_count
    )

    # Make pipeline decision based on quality gate
    if results['quality_gate']['decision'] in ['PASS', 'WARNING']:
        return True  # Continue to Silver layer
    else:
        raise Exception(f"Data quality check failed: {results['quality_gate']['decision']}")

# Usage in your pipeline
if run_data_quality_tests("/path/to/bronze/table", 10000):
    print("✅ Proceeding to Silver layer processing")
```

## Integration with Databricks

### Unity Catalog Integration
```python
# Configure for Unity Catalog
config = {
    "environment": {
        "catalog_name": "main",
        "schema_name": "bronze"
    }
}

framework = DataTestingFramework(config_dict=config)
results = framework.run_bronze_pipeline_tests(
    table_path="main.bronze.your_table"
)
```

### Delta Live Tables Integration
```python
# In your DLT pipeline
import dlt
from src.data_testing_framework import DataTestingFramework

@dlt.table
def bronze_orders():
    return spark.read.format("json").load("/path/to/source")

# Add quality testing as a separate pipeline step
def validate_bronze_orders():
    framework = DataTestingFramework()
    results = framework.run_bronze_pipeline_tests(
        table_path="main.bronze.orders"
    )

    if results['quality_gate']['decision'] not in ['PASS', 'WARNING']:
        raise Exception("Bronze layer quality validation failed")
```

## Configuration

The framework uses YAML configuration files for maximum flexibility:

```yaml
bronze_layer:
  tests:
    file_integrity: true
    schema_validation: true
    record_counting: true
    timestamp_validation: true
  thresholds:
    completeness_min: 0.95
    accuracy_min: 0.99
    
quality_gates:
  pass_threshold: 0.95
  warning_threshold: 0.85
  
notifications:
  email:
    enabled: true
    smtp_server: "your-smtp-server.com"
  slack:
    enabled: true
    webhook_url: "your-slack-webhook"
```

## Integration with Databricks

This framework is designed to integrate seamlessly with:
- **Delta Live Tables**: For continuous monitoring
- **Databricks Workflows**: For orchestration
- **Unity Catalog**: For governance and lineage
- **Auto Loader**: For file processing

## License

MIT License - see LICENSE file for details.
