#!/usr/bin/env python3
"""
Unified Data Quality Check Script

This script works both locally and in Databricks workflows.
It automatically detects the environment and adapts accordingly.

Local Usage:
    python unified_data_quality_check.py --table-path /path/to/table --expected-count 1000
    python unified_data_quality_check.py --test-scenario good
    python unified_data_quality_check.py --help

Databricks Usage:
    # Set notebook parameters and run
    dbutils.widgets.text("table_path", "main.bronze.your_table")
    dbutils.widgets.text("expected_count", "10000")
    %run ./unified_data_quality_check
"""

import os
import sys
import json
import argparse
import tempfile
import shutil
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Union
from pathlib import Path

# Detect environment
try:
    import dbutils
    DATABRICKS_ENV = True
    print("🏢 Running in Databricks environment")
except ImportError:
    DATABRICKS_ENV = False
    print("💻 Running in local environment")
    
    # Mock dbutils for local testing
    class MockDbutils:
        class widgets:
            _widgets = {}
            @classmethod
            def text(cls, name, default=""):
                cls._widgets[name] = default
            @classmethod
            def get(cls, name, default=""):
                return cls._widgets.get(name, default)
        class notebook:
            @staticmethod
            def exit(value):
                if isinstance(value, dict):
                    print(f"📋 Notebook Exit: {json.dumps(value, indent=2)}")
                    sys.exit(0 if value.get("status") == "success" else 1)
                else:
                    print(f"📋 Notebook Exit: {value}")
                    sys.exit(0)
        class secrets:
            @staticmethod
            def get(scope, key):
                return f"mock_{scope}_{key}"
    
    dbutils = MockDbutils()

# Add src to Python path
script_dir = Path(__file__).parent
src_dir = script_dir / "src"
if src_dir.exists():
    sys.path.insert(0, str(src_dir))

# Import framework components
try:
    from src.data_testing_framework import DataTestingFramework
    from src.utils.logging_config import configure_logging
    if not DATABRICKS_ENV:
        from tests.test_framework_with_sample_data import SampleDataGenerator
except ImportError as e:
    print(f"❌ Failed to import framework components: {e}")
    print("Make sure you're running from the project root directory")
    sys.exit(1)

# Import Spark
try:
    from pyspark.sql import SparkSession
    from pyspark.sql.types import StructType, StructField, StringType, IntegerType, DoubleType, TimestampType
except ImportError:
    print("❌ PySpark not available. Install with: pip install pyspark")
    sys.exit(1)


def get_parameters() -> Dict[str, Any]:
    """Get parameters from command line (local) or widgets (Databricks)"""
    
    if DATABRICKS_ENV:
        # Databricks: Get from widgets
        return {
            "table_path": dbutils.widgets.get("table_path", ""),
            "expected_count": int(dbutils.widgets.get("expected_count", "0")) or None,
            "test_scenario": dbutils.widgets.get("test_scenario", ""),
            "batch_id": dbutils.widgets.get("batch_id", f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}"),
            "source_system": dbutils.widgets.get("source_system", "unknown"),
            "data_date": dbutils.widgets.get("data_date", datetime.now().strftime("%Y-%m-%d")),
            "notification_level": dbutils.widgets.get("notification_level", "critical"),
            "fail_on_warning": dbutils.widgets.get("fail_on_warning", "false").lower() == "true",
            "send_notifications": dbutils.widgets.get("send_notifications", "false").lower() == "true"
        }
    else:
        # Local: Parse command line arguments
        parser = argparse.ArgumentParser(
            description="Unified Data Quality Check - Works locally and in Databricks",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Examples:
  # Test with real table
  python unified_data_quality_check.py --table-path /path/to/table --expected-count 1000
  
  # Test with sample data
  python unified_data_quality_check.py --test-scenario good
  python unified_data_quality_check.py --test-scenario poor
  python unified_data_quality_check.py --test-scenario bad
  
  # Enable notifications
  python unified_data_quality_check.py --table-path /path/to/table --send-notifications
            """
        )
        
        parser.add_argument("--table-path", help="Path to Bronze table to test")
        parser.add_argument("--expected-count", type=int, help="Expected number of records")
        parser.add_argument("--test-scenario", choices=["good", "poor", "bad"], 
                          help="Generate and test sample data scenario")
        parser.add_argument("--batch-id", default=f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        parser.add_argument("--source-system", default="test_system")
        parser.add_argument("--data-date", default=datetime.now().strftime("%Y-%m-%d"))
        parser.add_argument("--notification-level", choices=["critical", "warning", "info", "none"], 
                          default="none")
        parser.add_argument("--fail-on-warning", action="store_true")
        parser.add_argument("--send-notifications", action="store_true")
        
        args = parser.parse_args()
        
        return {
            "table_path": args.table_path or "",
            "expected_count": args.expected_count,
            "test_scenario": args.test_scenario or "",
            "batch_id": args.batch_id,
            "source_system": args.source_system,
            "data_date": args.data_date,
            "notification_level": args.notification_level,
            "fail_on_warning": args.fail_on_warning,
            "send_notifications": args.send_notifications
        }


def create_unified_config(params: Dict[str, Any]) -> Dict[str, Any]:
    """Create configuration that works in both environments"""
    
    return {
        "environment": {
            "name": "production" if DATABRICKS_ENV else "local_test",
            "catalog_name": "main" if DATABRICKS_ENV else "test_catalog",
            "schema_name": "bronze"
        },
        "bronze_layer": {
            "tests": {
                "file_integrity": True,
                "schema_validation": True,
                "record_counting": True,
                "timestamp_validation": True,
                "metadata_preservation": True,
                "duplicate_detection": True
            },
            "thresholds": {
                "completeness_min": 0.95,
                "accuracy_min": 0.98,
                "consistency_min": 0.95,
                "validity_min": 0.98,
                "timeliness_hours": 24,
                "uniqueness_min": 0.98,
                "duplicate_threshold": 0.05
            }
        },
        "quality_gates": {
            "pass_threshold": 0.95,
            "warning_threshold": 0.85,
            "quarantine": {
                "enabled": True,
                "storage_path": "/mnt/quarantine/bronze" if DATABRICKS_ENV else "/tmp/quarantine",
                "retention_days": 30
            },
            "escalation": {
                "emergency_contact_threshold": 0.5,
                "auto_recovery_enabled": False
            }
        },
        "notifications": {
            "email": {
                "enabled": params["send_notifications"] and params["notification_level"] != "none",
                "smtp_server": "smtp.company.com" if DATABRICKS_ENV else "localhost",
                "smtp_port": 587,
                "smtp_username": dbutils.secrets.get("email", "username") if DATABRICKS_ENV else "<EMAIL>",
                "smtp_password": dbutils.secrets.get("email", "password") if DATABRICKS_ENV else "test_password",
                "from_address": "<EMAIL>",
                "recipients": {
                    "critical": ["<EMAIL>", "<EMAIL>"],
                    "warning": ["<EMAIL>"],
                    "info": ["<EMAIL>"]
                }
            },
            "slack": {
                "enabled": False  # Disabled as requested
            }
        },
        "logging": {
            "level": "INFO",
            "destinations": {
                "console": True,
                "databricks": DATABRICKS_ENV,
                "file": not DATABRICKS_ENV
            },
            "databricks_logging": {
                "log_table": "main.logs.data_quality_logs"
            } if DATABRICKS_ENV else {}
        }
    }


def generate_sample_data(scenario: str, spark: SparkSession) -> Dict[str, Any]:
    """Generate sample data for testing (local environment only)"""
    
    if DATABRICKS_ENV:
        raise ValueError("Sample data generation not available in Databricks environment")
    
    print(f"📊 Generating sample data for scenario: {scenario}")
    
    data_generator = SampleDataGenerator(spark)
    
    if scenario == "good":
        sample_data = data_generator.create_good_quality_data(1000)
        print("✅ Generated high-quality sample data (expected: PASS)")
    elif scenario == "poor":
        sample_data = data_generator.create_poor_quality_data(1000)
        print("⚠️ Generated poor-quality sample data (expected: WARNING/FAIL)")
    elif scenario == "bad":
        sample_data = data_generator.create_corrupted_data(1000)
        print("❌ Generated corrupted sample data (expected: FAIL/QUARANTINE)")
    else:
        raise ValueError(f"Unknown scenario: {scenario}")
    
    # Save to temporary location
    temp_dir = tempfile.mkdtemp()
    table_path = os.path.join(temp_dir, "test_table")
    
    df = sample_data["dataframe"]
    df.write.format("delta").mode("overwrite").save(table_path)
    
    print(f"💾 Sample data saved to: {table_path}")
    
    return {
        "table_path": table_path,
        "sample_data": sample_data,
        "temp_dir": temp_dir
    }


def validate_parameters(params: Dict[str, Any]) -> bool:
    """Validate parameters"""
    
    if not params["table_path"] and not params["test_scenario"]:
        print("❌ ERROR: Either --table-path or --test-scenario must be specified")
        return False
    
    if params["table_path"] and params["test_scenario"]:
        print("❌ ERROR: Cannot specify both --table-path and --test-scenario")
        return False
    
    return True


def determine_workflow_action(results: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
    """Determine workflow action based on results"""
    
    decision = results['quality_gate']['decision']
    
    if decision == "PASS":
        return {
            "status": "success",
            "action": "proceed",
            "message": "✅ Data quality check PASSED - proceed to Silver layer",
            "continue_pipeline": True,
            "exit_code": 0
        }
    elif decision == "WARNING":
        if params["fail_on_warning"]:
            return {
                "status": "warning_blocked",
                "action": "stop",
                "message": "⚠️ Data quality WARNING - pipeline blocked due to fail_on_warning=true",
                "continue_pipeline": False,
                "exit_code": 1
            }
        else:
            return {
                "status": "warning_proceed",
                "action": "proceed_with_caution",
                "message": "⚠️ Data quality WARNING - proceeding with caution",
                "continue_pipeline": True,
                "exit_code": 0
            }
    elif decision in ["FAIL", "QUARANTINE"]:
        return {
            "status": "failed",
            "action": "stop",
            "message": f"❌ Data quality check FAILED ({decision}) - pipeline blocked",
            "continue_pipeline": False,
            "exit_code": 1
        }
    elif decision == "EMERGENCY":
        return {
            "status": "emergency",
            "action": "halt_all",
            "message": "🚨 EMERGENCY: Critical data quality issues - all processing halted",
            "continue_pipeline": False,
            "exit_code": 2
        }
    else:
        return {
            "status": "unknown",
            "action": "stop",
            "message": f"❓ Unknown quality gate decision: {decision}",
            "continue_pipeline": False,
            "exit_code": 1
        }


def save_results_to_delta(results: Dict[str, Any], params: Dict[str, Any], spark: SparkSession) -> None:
    """Save results to Delta table (Databricks only)"""
    
    if not DATABRICKS_ENV:
        print("ℹ️ Skipping Delta table save (local environment)")
        return
    
    try:
        results_data = {
            "execution_id": results['execution_id'],
            "execution_timestamp": datetime.now(),
            "table_path": params['table_path'],
            "batch_id": params['batch_id'],
            "source_system": params['source_system'],
            "data_date": params['data_date'],
            "quality_decision": results['quality_gate']['decision'],
            "overall_score": results['quality_gate']['overall_score'],
            "threshold_met": results['quality_gate']['threshold_met'],
            "total_tests": results['test_summary']['total_tests'],
            "passed_tests": results['test_summary']['passed'],
            "warning_tests": results['test_summary']['warnings'],
            "failed_tests": results['test_summary']['failed'],
            "execution_time_seconds": results['execution_time_seconds'],
            "quarantine_required": results['quality_gate']['quarantine_required'],
            "emergency_escalation": results['quality_gate']['emergency_escalation'],
            "framework_version": results['framework_metadata']['version'],
            "environment": "databricks"
        }
        
        results_df = spark.createDataFrame([results_data])
        results_table = "main.logs.bronze_quality_results"
        results_df.write.mode("append").option("mergeSchema", "true").saveAsTable(results_table)
        
        print(f"✅ Results saved to {results_table}")
        
    except Exception as e:
        print(f"⚠️ Failed to save results to Delta table: {e}")


def display_results(results: Dict[str, Any], params: Dict[str, Any]) -> None:
    """Display comprehensive results"""
    
    print("\n" + "=" * 60)
    print("📊 DATA QUALITY TEST RESULTS")
    print("=" * 60)
    
    print(f"🆔 Execution ID: {results['execution_id']}")
    print(f"📁 Table Path: {params['table_path']}")
    print(f"⏱️  Execution Time: {results['execution_time_seconds']:.2f} seconds")
    print(f"🕐 Completed: {results['end_time']}")
    
    # Quality Gate Decision
    quality_gate = results['quality_gate']
    decision_emoji = {
        "PASS": "✅",
        "WARNING": "⚠️",
        "FAIL": "❌",
        "QUARANTINE": "🔒",
        "EMERGENCY": "🚨"
    }
    
    emoji = decision_emoji.get(quality_gate['decision'], '📊')
    print(f"\n{emoji} Quality Gate: {quality_gate['decision']}")
    print(f"📈 Overall Score: {quality_gate['overall_score']:.2%}")
    print(f"🎯 Threshold Met: {'Yes' if quality_gate['threshold_met'] else 'No'}")
    
    if quality_gate['quarantine_required']:
        print("🔒 QUARANTINE REQUIRED - Data moved to quarantine storage")
    
    if quality_gate['emergency_escalation']:
        print("🚨 EMERGENCY ESCALATION - Immediate attention required!")
    
    # Test Summary
    test_summary = results['test_summary']
    print(f"\n📋 TEST SUMMARY")
    print(f"{'Metric':<20} {'Count':<10}")
    print("-" * 30)
    print(f"{'Total Tests':<20} {test_summary['total_tests']:<10}")
    print(f"{'Passed':<20} {test_summary['passed']:<10}")
    print(f"{'Warnings':<20} {test_summary['warnings']:<10}")
    print(f"{'Failed':<20} {test_summary['failed']:<10}")
    print(f"{'Errors':<20} {test_summary['errors']:<10}")
    
    # Individual Test Results
    print(f"\n🔍 INDIVIDUAL TEST RESULTS")
    print(f"{'Test Name':<25} {'Status':<12} {'Score':<10} {'Duration':<10}")
    print("-" * 67)
    
    test_suite = results['test_suite']
    for test_result in test_suite['test_results']:
        test_name = test_result['test_name']
        status = test_result['status']
        score = f"{test_result.get('overall_score', 0):.1%}"
        duration = f"{test_result['execution_time_seconds']:.2f}s"
        
        status_emoji = {"PASS": "✅", "WARNING": "⚠️", "FAIL": "❌", "ERROR": "💥"}
        status_display = f"{status_emoji.get(status, '❓')} {status}"
        
        print(f"{test_name:<25} {status_display:<12} {score:<10} {duration:<10}")
    
    # Recommendations
    if quality_gate['recommendations']:
        print(f"\n💡 RECOMMENDATIONS")
        for i, rec in enumerate(quality_gate['recommendations'], 1):
            print(f"  {i}. {rec}")
    
    # Next Actions
    print(f"\n🎯 NEXT ACTIONS")
    for i, action in enumerate(results['next_actions'], 1):
        print(f"  {i}. {action}")


def main():
    """Main function"""
    
    temp_dir = None
    
    try:
        # Get parameters
        params = get_parameters()
        
        # Validate parameters
        if not validate_parameters(params):
            if DATABRICKS_ENV:
                dbutils.notebook.exit({"status": "error", "message": "Invalid parameters", "exit_code": 1})
            else:
                sys.exit(1)
        
        # Configure logging
        configure_logging({
            "level": "INFO",
            "destinations": {"console": True, "databricks": DATABRICKS_ENV}
        })
        
        # Initialize Spark
        if DATABRICKS_ENV:
            spark = SparkSession.getActiveSession()
            if not spark:
                spark = SparkSession.builder.appName("BronzeDataQualityCheck").getOrCreate()
        else:
            print("⚡ Initializing Spark session...")
            spark = SparkSession.builder \
                .appName("DataQualityCheck") \
                .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension") \
                .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog") \
                .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer") \
                .getOrCreate()
        
        # Handle sample data generation (local only)
        if params["test_scenario"]:
            if DATABRICKS_ENV:
                print("❌ Sample data generation not supported in Databricks environment")
                dbutils.notebook.exit({"status": "error", "message": "Sample data not supported in Databricks", "exit_code": 1})
            
            sample_info = generate_sample_data(params["test_scenario"], spark)
            params["table_path"] = sample_info["table_path"]
            params["expected_count"] = sample_info["sample_data"]["record_count"]
            temp_dir = sample_info["temp_dir"]
        
        # Log execution details
        print("\n🚀 BRONZE LAYER DATA QUALITY CHECK")
        print("=" * 50)
        print(f"🏢 Environment: {'Databricks' if DATABRICKS_ENV else 'Local'}")
        print(f"📊 Table Path: {params['table_path']}")
        print(f"🔢 Expected Count: {params['expected_count'] or 'Not specified'}")
        print(f"📦 Batch ID: {params['batch_id']}")
        print(f"🏢 Source System: {params['source_system']}")
        print(f"📅 Data Date: {params['data_date']}")
        print("=" * 50)
        
        # Create configuration
        config = create_unified_config(params)
        
        # Initialize framework
        print("🔧 Initializing Data Testing Framework...")
        framework = DataTestingFramework(config_dict=config, spark=spark)
        
        # Validate configuration
        validation = framework.validate_configuration()
        if not validation["config_valid"]:
            print("❌ Framework configuration validation failed:")
            for issue in validation["issues"]:
                print(f"   • {issue}")
            
            exit_data = {"status": "config_error", "message": "Framework configuration invalid", "exit_code": 1}
            if DATABRICKS_ENV:
                dbutils.notebook.exit(exit_data)
            else:
                sys.exit(1)
        
        # Prepare source metadata
        source_metadata = {
            "source_system": params["source_system"],
            "batch_id": params["batch_id"],
            "data_date": params["data_date"],
            "extraction_timestamp": datetime.now().isoformat(),
            "environment": "databricks" if DATABRICKS_ENV else "local"
        }
        
        # Run data quality tests
        print("🔍 Running Bronze layer data quality tests...")
        results = framework.run_bronze_pipeline_tests(
            table_path=params["table_path"],
            expected_count=params["expected_count"],
            source_metadata=source_metadata,
            timestamp_column="ingestion_timestamp",
            send_notifications=params["send_notifications"]
        )
        
        # Save results to Delta (Databricks only)
        save_results_to_delta(results, params, spark)
        
        # Display results
        display_results(results, params)
        
        # Determine workflow action
        workflow_action = determine_workflow_action(results, params)
        
        # Final status
        print("\n" + "=" * 60)
        print("🚦 WORKFLOW DECISION")
        print("=" * 60)
        print(f"📊 Quality Decision: {results['quality_gate']['decision']}")
        print(f"📈 Overall Score: {results['quality_gate']['overall_score']:.2%}")
        print(f"🎯 Workflow Action: {workflow_action['action']}")
        print(f"💬 Message: {workflow_action['message']}")
        print("=" * 60)
        
        # Exit with appropriate status
        exit_data = {
            "status": workflow_action["status"],
            "action": workflow_action["action"],
            "message": workflow_action["message"],
            "continue_pipeline": workflow_action["continue_pipeline"],
            "quality_decision": results['quality_gate']['decision'],
            "overall_score": results['quality_gate']['overall_score'],
            "execution_id": results['execution_id'],
            "exit_code": workflow_action["exit_code"]
        }
        
        if DATABRICKS_ENV:
            dbutils.notebook.exit(exit_data)
        else:
            print(f"\n📋 Exit Status: {json.dumps(exit_data, indent=2)}")
            sys.exit(workflow_action["exit_code"])
        
    except Exception as e:
        error_message = f"❌ Data quality check failed: {str(e)}"
        print(error_message)
        
        import traceback
        traceback.print_exc()
        
        exit_data = {"status": "error", "message": error_message, "exit_code": 1}
        
        if DATABRICKS_ENV:
            dbutils.notebook.exit(exit_data)
        else:
            sys.exit(1)
    
    finally:
        # Cleanup temporary files (local only)
        if temp_dir and os.path.exists(temp_dir):
            shutil.rmtree(temp_dir, ignore_errors=True)
            print("🧹 Temporary files cleaned up")
        
        if not DATABRICKS_ENV and 'spark' in locals():
            spark.stop()
            print("🧹 Spark session stopped")


if __name__ == "__main__":
    main()
