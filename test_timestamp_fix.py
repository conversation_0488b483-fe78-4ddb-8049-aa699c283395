#!/usr/bin/env python3
"""
Test script to demonstrate the fixed timestamp parsing functionality.
This script shows how to handle the '05-Jun-24' format that was causing errors.
"""

from pyspark.sql import SparkSession
from spark_type_inference import cast_to_inferred_type, validate_timestamp_conversion


def test_problematic_timestamps():
    """Test the specific timestamp formats that were causing issues"""
    
    # Initialize Spark
    spark = SparkSession.builder \
        .appName("TimestampFixTest") \
        .config("spark.sql.adaptive.enabled", "false") \
        .getOrCreate()
    
    # Sample data with the problematic formats from your error
    test_data = [
        ("1", "05-Jun-24", "100.50", "active"),
        ("2", "20-Nov-24", "200.75", "inactive"), 
        ("3", "12/2/2024 12:00:00 AM", "300.25", "active"),
        ("4", "2024-12-03 15:30:00", "400.00", "pending"),
        ("5", "invalid_date", "500.00", "error"),  # This should become null
        ("6", "15-Dec-24", "600.25", "active")
    ]
    
    columns = ["id", "trade_date", "amount", "status"]
    df = spark.createDataFrame(test_data, columns)
    
    print("🔍 ORIGINAL DATAFRAME:")
    print("=" * 50)
    df.show(truncate=False)
    df.printSchema()
    
    print("\n🛠️  APPLYING TYPE INFERENCE WITH ROBUST TIMESTAMP HANDLING:")
    print("=" * 70)
    
    # Apply the fixed type inference
    try:
        typed_df = cast_to_inferred_type(df, debug=True)
        
        print("\n✅ SUCCESS! TYPED DATAFRAME:")
        print("=" * 40)
        typed_df.show(truncate=False)
        typed_df.printSchema()
        
        # Validate the timestamp conversion
        print("\n📊 TIMESTAMP VALIDATION RESULTS:")
        print("=" * 40)
        validation_results = validate_timestamp_conversion(typed_df, ['trade_date'])
        
        # Try to write the DataFrame (this was failing before)
        print("\n💾 TESTING WRITE OPERATION:")
        print("=" * 30)
        
        # Write to a temporary location to test if the casting fixed the issue
        output_path = "/tmp/test_timestamp_output"
        typed_df.write.mode("overwrite").option("overwriteSchema", "true").saveAsTable("temp_test_table")
        
        print("✅ Write operation successful! No more timestamp parsing errors.")
        
        # Show some statistics
        print(f"\n📈 SUMMARY:")
        print(f"Total rows processed: {typed_df.count()}")
        print(f"Timestamp column successfully cast: trade_date")
        print(f"Validation status: {validation_results['trade_date']['status']}")
        
        return typed_df
        
    except Exception as e:
        print(f"❌ Error occurred: {str(e)}")
        raise
    
    finally:
        spark.stop()


def test_write_with_delta():
    """Test writing with Delta format (common in Databricks)"""
    
    spark = SparkSession.builder \
        .appName("DeltaWriteTest") \
        .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension") \
        .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog") \
        .getOrCreate()
    
    # Your problematic data
    test_data = [
        ("trade1", "05-Jun-24", 1000.0, "USD"),
        ("trade2", "15-Jul-24", 2000.0, "EUR"),
        ("trade3", "25-Aug-24", 3000.0, "GBP")
    ]
    
    columns = ["trade_id", "trade_date", "amount", "currency"]
    df = spark.createDataFrame(test_data, columns)
    
    print("🔄 TESTING DELTA WRITE WITH FIXED TIMESTAMPS:")
    print("=" * 50)
    
    # Apply type inference
    typed_df = cast_to_inferred_type(df, debug=False)
    
    # Test Delta write
    try:
        typed_df.write.format("delta").mode("overwrite") \
            .option("overwriteSchema", "true") \
            .save("/tmp/delta_test_output")
        
        print("✅ Delta write successful!")
        
        # Read it back to verify
        read_df = spark.read.format("delta").load("/tmp/delta_test_output")
        print("\n📖 READ BACK FROM DELTA:")
        read_df.show()
        read_df.printSchema()
        
    except Exception as e:
        print(f"❌ Delta write failed: {str(e)}")
    
    finally:
        spark.stop()


if __name__ == "__main__":
    print("🚀 TESTING TIMESTAMP PARSING FIX")
    print("=" * 50)
    
    # Test 1: Basic timestamp parsing
    test_problematic_timestamps()
    
    print("\n" + "="*70 + "\n")
    
    # Test 2: Delta format writing (uncomment if you have Delta Lake)
    # test_write_with_delta()
    
    print("🎉 All tests completed!")
