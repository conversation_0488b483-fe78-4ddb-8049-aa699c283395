# 🚀 DataFrame-Based Data Quality Framework

## ✅ **COMPLETED: Entity-Agnostic Testing**

The data quality framework now supports **DataFrame-based testing**, making it completely **entity-agnostic** and reusable across any data source.

## 🎯 **Key Features**

### 1. **Direct DataFrame Input**
```python
from data_quality_check import run_data_quality_tests

# Test any DataFrame
result = run_data_quality_tests(
    df=your_dataframe,
    expected_count=1000,
    expected_schema=your_expected_schema,
    table_name="orders",
    batch_id="batch_001",
    source_system="ecommerce_api"
)
```

### 2. **Multiple Configuration Sources**

#### **A. Pipeline Control Table**
```python
# Load configuration from control table
control_config = load_from_control_table(spark, "main.control.pipeline_metadata", "main.bronze.orders")
result = run_data_quality_tests(
    df=df,
    expected_count=control_config["expected_count"],
    expected_schema=control_config["expected_schema"],
    table_name="orders"
)
```

#### **B. JSON Schema Files**
```python
# Load schema from JSON file
expected_schema = load_schema_from_json("schemas/orders_schema.json")
result = run_data_quality_tests(
    df=df,
    expected_schema=expected_schema,
    table_name="orders"
)
```

#### **C. Direct Parameters**
```python
# Direct parameter specification
result = run_data_quality_tests(
    df=df,
    expected_count=1000,
    table_name="orders"
)
```

### 3. **Schema Validation Results**

The framework now **properly validates schemas** and detects real differences:

```
🔍 TEST 2: With expected schema validation
❌ schema_validation: FAIL (327.5%)
🚨 Decision: EMERGENCY
```

**Schema validation correctly identifies:**
- ✅ **Missing columns** (expected but not present)
- ✅ **Extra columns** (present but not expected)  
- ✅ **Type mismatches** (wrong data types)
- ✅ **Nullable mismatches** (wrong nullable settings)

## 📊 **Usage Examples**

### **Example 1: Basic DataFrame Testing**
```python
from pyspark.sql import SparkSession
from data_quality_check import run_data_quality_tests

spark = SparkSession.builder.appName("DataQuality").getOrCreate()

# Your DataFrame from any source
df = spark.table("your_database.your_table")

# Run quality tests
result = run_data_quality_tests(
    df=df,
    expected_count=1000,
    table_name="orders",
    batch_id="batch_001",
    source_system="ecommerce_api",
    fail_on_warning=False,
    send_notifications=False
)

# Check results
if result["continue_pipeline"]:
    print("✅ Quality checks passed")
else:
    print(f"❌ Quality issues: {result['message']}")
```

### **Example 2: With External Schema Validation**
```python
import json
from pyspark.sql.types import StructType

# Load expected schema from JSON
with open('schemas/orders_schema.json', 'r') as f:
    schema_dict = json.load(f)
expected_schema = StructType.fromJson(schema_dict)

# Test with schema validation
result = run_data_quality_tests(
    df=df,
    expected_count=1000,
    expected_schema=expected_schema,
    table_name="orders",
    batch_id="batch_001",
    source_system="ecommerce_api"
)
```

### **Example 3: Integration with Pipeline Control Table**
```python
# Simulate loading from control table
control_config = {
    "expected_count": 1000,
    "expected_schema": expected_schema,
    "fail_on_warning": True
}

result = run_data_quality_tests(
    df=df,
    expected_count=control_config["expected_count"],
    expected_schema=control_config["expected_schema"],
    table_name="orders",
    fail_on_warning=control_config["fail_on_warning"]
)
```

## 🔧 **Function Parameters**

```python
def run_data_quality_tests(
    df: DataFrame,                    # Required: DataFrame to test
    expected_count: Optional[int] = None,      # Expected record count
    expected_schema: Optional[StructType] = None,  # Expected schema
    table_name: str = "unknown_table",         # Entity/table name
    batch_id: str = None,                      # Batch identifier
    source_system: str = "unknown",            # Source system name
    fail_on_warning: bool = False,             # Fail on warnings
    send_notifications: bool = False,          # Send notifications
    spark: SparkSession = None                 # Spark session
) -> Dict[str, Any]
```

## 📋 **Return Values**

```python
{
    "status": "success|error|emergency|quarantine",
    "action": "continue|stop|halt_all",
    "message": "Human readable message",
    "continue_pipeline": True|False,
    "quality_decision": "PASS|WARNING|QUARANTINE|EMERGENCY",
    "overall_score": 0.95,  # 0.0 to 1.0
    "execution_id": "uuid",
    "exit_code": 0|1|2,
    "results": {...}  # Full test results
}
```

## 🎯 **Benefits**

1. **Entity-Agnostic**: Works with any DataFrame from any source
2. **Flexible Configuration**: Multiple ways to specify expectations
3. **Real Schema Validation**: Detects actual schema differences
4. **Pipeline Integration**: Easy to integrate into existing pipelines
5. **Dual Environment**: Works both locally and in Databricks
6. **Comprehensive Testing**: File integrity, schema, counts, timestamps, metadata

## 🚀 **Next Steps**

The framework is now ready for production use with any DataFrame. You can:

1. **Integrate into existing pipelines** by calling `run_data_quality_tests()` with your DataFrames
2. **Set up control tables** to centralize configuration management
3. **Create JSON schema files** for each entity you want to validate
4. **Configure notifications** for quality gate failures
5. **Customize quality thresholds** based on your business requirements

The framework provides enterprise-grade data quality testing that scales across all your data entities! 🎉
