#!/usr/bin/env python3
"""
Simple test of the DataFrame-based data quality function
"""

import json
from pyspark.sql import SparkSession
from pyspark.sql.types import StructType, StructField, StringType, IntegerType, DoubleType, TimestampType
from datetime import datetime

# Import the data quality function
from data_quality_check import run_data_quality_tests


def main():
    """Simple test of DataFrame functionality"""
    
    # Initialize Spark
    spark = SparkSession.builder \
        .appName("DataQualityTest") \
        .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer") \
        .getOrCreate()
    
    print("🧪 TESTING DATAFRAME-BASED DATA QUALITY FUNCTION")
    print("=" * 50)
    
    # Create a simple test DataFrame
    schema = StructType([
        StructField("id", StringType(), False),
        StructField("name", StringType(), False),
        StructField("value", IntegerType(), True),
        StructField("ingestion_timestamp", TimestampType(), False)
    ])
    
    data = [
        ("1", "test1", 100, datetime.now()),
        ("2", "test2", 200, datetime.now()),
        ("3", "test3", 300, datetime.now())
    ]
    
    df = spark.createDataFrame(data, schema)
    
    print("📊 Created test DataFrame with 3 records")
    df.show()
    
    # Test 1: Basic validation without expected schema
    print("\n🔍 TEST 1: Basic validation (no expected schema)")
    result1 = run_data_quality_tests(
        df=df,
        expected_count=3,
        table_name="test_table",
        batch_id="test_batch_001",
        source_system="test_system",
        spark=spark
    )
    
    print(f"Result: {result1['status']}")
    print(f"Quality Decision: {result1.get('quality_decision', 'N/A')}")
    print(f"Overall Score: {result1.get('overall_score', 0):.1%}")
    print(f"Continue Pipeline: {result1.get('continue_pipeline', False)}")
    
    # Test 2: With expected schema
    print("\n🔍 TEST 2: With expected schema validation")
    
    # Load expected schema from JSON
    with open('examples/orders_schema.json', 'r') as f:
        schema_dict = json.load(f)
    expected_schema = StructType.fromJson(schema_dict)
    
    result2 = run_data_quality_tests(
        df=df,
        expected_count=3,
        expected_schema=expected_schema,  # This should fail - different schema
        table_name="test_table_with_schema",
        batch_id="test_batch_002",
        source_system="test_system",
        spark=spark
    )
    
    print(f"Result: {result2['status']}")
    print(f"Quality Decision: {result2.get('quality_decision', 'N/A')}")
    print(f"Overall Score: {result2.get('overall_score', 0):.1%}")
    print(f"Continue Pipeline: {result2.get('continue_pipeline', False)}")
    
    print("\n✅ DataFrame function tests completed!")
    
    spark.stop()


if __name__ == "__main__":
    main()
