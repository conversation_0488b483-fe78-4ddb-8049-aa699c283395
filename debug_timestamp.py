#!/usr/bin/env python3
"""
Quick debug script to test the exact timestamp format that's failing.
This will help verify the fix works for '20-Nov-24' format.
"""

from pyspark.sql import SparkSession
from pyspark.sql.functions import to_timestamp, coalesce, col, lit


def test_exact_failing_format():
    """Test the exact format that was causing the error"""
    
    spark = SparkSession.builder.appName("DebugTimestamp").getOrCreate()
    
    # Create DataFrame with the exact failing value
    failing_data = [
        ("20-Nov-24",),
        ("05-Jun-24",),
        ("15-Dec-24",),
        ("01-Jan-25",)
    ]
    
    df = spark.createDataFrame(failing_data, ["date_str"])
    
    print("🔍 TESTING EXACT FAILING FORMATS:")
    print("=" * 40)
    df.show()
    
    # Test individual patterns to see which one works
    patterns_to_test = [
        ("dd-MMM-yy", "Standard 2-digit year"),
        ("dd-MMM-yyyy", "4-digit year"),
        ("d-MMM-yy", "Single digit day, 2-digit year"),
        ("dd-MMM-yy", "Double digit day, 2-digit year")
    ]
    
    for pattern, description in patterns_to_test:
        print(f"\n🧪 Testing pattern: {pattern} ({description})")
        try:
            test_df = df.withColumn("parsed_date", to_timestamp(col("date_str"), pattern))
            test_df.select("date_str", "parsed_date").show()
            
            # Check success rate
            total = test_df.count()
            nulls = test_df.filter(col("parsed_date").isNull()).count()
            success_rate = (total - nulls) / total * 100
            print(f"✅ Success rate: {success_rate:.1f}%")
            
        except Exception as e:
            print(f"❌ Pattern failed: {str(e)}")
    
    # Test the coalesce approach
    print(f"\n🔧 Testing COALESCE approach (multiple patterns):")
    coalesce_df = df.withColumn("parsed_date", 
        coalesce(
            to_timestamp(col("date_str"), "dd-MMM-yy"),
            to_timestamp(col("date_str"), "d-MMM-yy"),
            to_timestamp(col("date_str"), "dd-MMM-yyyy"),
            to_timestamp(col("date_str"))
        )
    )
    
    coalesce_df.show()
    
    # Check final success rate
    total = coalesce_df.count()
    nulls = coalesce_df.filter(col("parsed_date").isNull()).count()
    success_rate = (total - nulls) / total * 100
    print(f"🎯 Final success rate: {success_rate:.1f}%")
    
    spark.stop()
    return success_rate


def test_with_your_data_format():
    """Test with a format similar to your actual data"""
    
    spark = SparkSession.builder.appName("YourDataTest").getOrCreate()
    
    # Simulate your actual data structure
    your_data = [
        ("trade1", "20-Nov-24", 1000.0),
        ("trade2", "05-Jun-24", 2000.0),
        ("trade3", "15-Dec-24", 3000.0),
        ("trade4", "invalid", 4000.0)  # This should become null
    ]
    
    df = spark.createDataFrame(your_data, ["trade_id", "date_str", "amount"])
    
    print("\n🏢 TESTING WITH YOUR DATA STRUCTURE:")
    print("=" * 45)
    df.show()
    
    # Apply the fix
    from spark_type_inference import cast_timestamp_column_robust
    
    fixed_df = cast_timestamp_column_robust(df, "date_str", "date_str", debug=True)
    
    print("\n✅ AFTER APPLYING FIX:")
    fixed_df.show()
    fixed_df.printSchema()
    
    # Test write operation
    try:
        fixed_df.write.mode("overwrite").saveAsTable("test_timestamp_table")
        print("🎉 WRITE OPERATION SUCCESSFUL!")
    except Exception as e:
        print(f"❌ Write failed: {str(e)}")
    
    spark.stop()


if __name__ == "__main__":
    print("🚀 DEBUGGING TIMESTAMP PARSING ISSUE")
    print("=" * 50)
    
    # Test 1: Individual patterns
    success_rate = test_exact_failing_format()
    
    if success_rate >= 75:
        print(f"\n✅ Pattern testing successful ({success_rate:.1f}% success rate)")
        
        # Test 2: Full integration
        test_with_your_data_format()
    else:
        print(f"\n❌ Pattern testing failed ({success_rate:.1f}% success rate)")
        print("Need to investigate further...")
