# Testing the Data Testing Framework

This guide shows you how to test the framework with sample data before deploying to Databricks.

## Quick Test

The easiest way to test the framework:

```bash
# Install dependencies
pip install -r requirements.txt

# Run all test scenarios
python run_tests.py

# Or run a specific scenario
python run_tests.py --scenario good    # High quality data (should PASS)
python run_tests.py --scenario poor    # Poor quality data (should WARNING)
python run_tests.py --scenario bad     # Corrupted data (should FAIL)
```

## What the Tests Do

### 1. Good Quality Data Test
- Creates 1,000 clean records with proper schema
- All quality dimensions score 95%+ 
- **Expected Result**: PASS ✅
- **Pipeline Action**: Proceed to Silver layer

### 2. Poor Quality Data Test  
- Introduces quality issues:
  - 10% missing product names
  - 5% missing emails
  - 2% negative quantities
  - 3% inconsistent amounts
  - 10% old timestamps
  - 2% duplicate records
- **Expected Result**: WARNING ⚠️ or FAIL ❌
- **Pipeline Action**: Proceed with caution or block

### 3. Corrupted Data Test
- Severe data corruption:
  - 50% missing order IDs
  - 30% missing customer IDs  
  - 20% invalid quantities
  - All timestamps 30+ days old
  - 20% duplicate records
- **Expected Result**: FAIL ❌ or QUARANTINE 🔒
- **Pipeline Action**: Block pipeline, quarantine data

## Sample Output

```
🧪 TESTING SCENARIO: High Quality Data
============================================================
📊 Generating sample data...
✅ Generated 1000 records
📋 Schema: 14 columns
💾 Saved test data to: /tmp/test_table

🔍 Running data quality tests...

📊 TEST RESULTS:
   Execution ID: 12345678-1234-1234-1234-123456789012
   Execution Time: 2.45 seconds

✅ Quality Gate Decision: PASS
📈 Overall Score: 98.5%
🎯 Expected Decision: PASS
✅ Test Result: PASS

📋 Test Summary:
   Total Tests: 6
   Passed: 6
   Warnings: 0
   Failed: 0
   Errors: 0

🚦 Pipeline Decision:
   ✅ PROCEED to Silver layer processing
```

## Testing Individual Components

### Test Configuration Only
```python
from src.data_testing_framework import DataTestingFramework

# Test config loading
framework = DataTestingFramework("config/test_config.yaml")
validation = framework.validate_configuration()
print(f"Config valid: {validation['config_valid']}")
```

### Test with Your Own Data
```python
from src.data_testing_framework import DataTestingFramework

framework = DataTestingFramework("config/test_config.yaml")

results = framework.run_bronze_pipeline_tests(
    table_path="/path/to/your/table",
    expected_count=5000,
    send_notifications=False  # Disable for testing
)

print(f"Decision: {results['quality_gate']['decision']}")
```

## Databricks Testing

### 1. Upload Framework to Databricks
```bash
# Create a zip file
zip -r data_testing_framework.zip src/ config/ requirements.txt

# Upload to Databricks workspace or DBFS
```

### 2. Create Test Notebook
```python
# Databricks notebook cell
%pip install -r requirements.txt

# Import and test
from src.data_testing_framework import DataTestingFramework

config = {
    "bronze_layer": {"tests": {"file_integrity": True}},
    "quality_gates": {"pass_threshold": 0.95},
    "notifications": {"email": {"enabled": False}, "slack": {"enabled": False}}
}

framework = DataTestingFramework(config_dict=config)
print("✅ Framework initialized in Databricks!")
```

### 3. Test with Delta Table
```python
# Create sample Delta table
df = spark.range(1000).toDF("id")
df.write.format("delta").mode("overwrite").saveAsTable("test.bronze.sample")

# Test framework
results = framework.run_bronze_pipeline_tests(
    table_path="test.bronze.sample",
    expected_count=1000
)
```

## Workflow Integration Testing

Use the workflow integration example:

```python
# In Databricks notebook
%run ./examples/databricks_workflow_integration

# Set parameters
dbutils.widgets.text("table_path", "main.bronze.your_table")
dbutils.widgets.text("expected_count", "10000")
dbutils.widgets.text("batch_id", "test_batch_001")

# Run the workflow
main()
```

## Troubleshooting

### Common Issues

1. **Import Errors**
   ```bash
   # Make sure you're in the project root
   python -c "from src.data_testing_framework import DataTestingFramework; print('OK')"
   ```

2. **Spark Issues**
   ```bash
   # Check Spark installation
   python -c "import pyspark; print(pyspark.__version__)"
   ```

3. **Delta Lake Issues**
   ```bash
   # Install Delta Lake
   pip install delta-spark
   ```

4. **Memory Issues**
   ```bash
   # Reduce test data size
   python run_tests.py --scenario good  # Uses smaller dataset
   ```

### Debug Mode

Enable debug logging:

```python
from src.utils.logging_config import configure_logging

configure_logging({
    "level": "DEBUG",
    "format": "console",
    "destinations": {"console": True}
})
```

## Expected Test Results

| Scenario | Overall Score | Decision | Pipeline Action |
|----------|---------------|----------|-----------------|
| Good Quality | 95-100% | PASS | ✅ Proceed |
| Poor Quality | 80-94% | WARNING | ⚠️ Proceed with caution |
| Corrupted | <80% | FAIL/QUARANTINE | ❌ Block pipeline |

## Next Steps

After successful testing:

1. **Configure for Production**
   - Update `config/config.yaml` with your settings
   - Set up email SMTP configuration
   - Configure Databricks secrets for credentials

2. **Deploy to Databricks**
   - Upload framework to workspace
   - Create workflow jobs
   - Set up monitoring and alerting

3. **Integrate with Pipelines**
   - Add quality checks after Bronze layer ETL
   - Configure workflow parameters
   - Set up failure handling and notifications

## Performance Notes

- Test data generation: ~1-2 seconds for 1,000 records
- Framework execution: ~2-5 seconds per test scenario
- Memory usage: ~100-200MB for test datasets
- Spark overhead: ~10-15 seconds for session startup

For production workloads, expect:
- 10,000 records: ~5-10 seconds
- 100,000 records: ~30-60 seconds  
- 1M+ records: ~2-5 minutes
