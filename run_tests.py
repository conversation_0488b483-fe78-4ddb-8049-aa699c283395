#!/usr/bin/env python3
"""
Simple Test Runner for Data Testing Framework

This script provides an easy way to test the framework with sample data.
Perfect for validating the framework before deploying to Databricks.

Usage:
    python run_tests.py                    # Run all test scenarios
    python run_tests.py --scenario good    # Run only good quality data test
    python run_tests.py --scenario poor    # Run only poor quality data test
    python run_tests.py --scenario bad     # Run only corrupted data test
    python run_tests.py --email            # Enable email notifications (requires SMTP config)
"""

import argparse
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from tests.test_framework_with_sample_data import main as run_comprehensive_tests
from tests.test_framework_with_sample_data import test_scenario, SampleDataGenerator, create_test_config
from src.utils.logging_config import configure_logging
from pyspark.sql import SparkSession


def run_single_scenario(scenario: str, email_enabled: bool = False):
    """Run a single test scenario"""
    
    print(f"🚀 RUNNING SINGLE SCENARIO: {scenario.upper()}")
    print("="*50)
    
    # Configure logging
    configure_logging({
        "level": "INFO",
        "format": "console",
        "destinations": {"console": True, "file": False}
    })
    
    # Initialize Spark
    print("⚡ Initializing Spark session...")
    spark = SparkSession.builder \
        .appName(f"DataQualityTest_{scenario}") \
        .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension") \
        .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog") \
        .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer") \
        .getOrCreate()
    
    # Initialize data generator
    data_generator = SampleDataGenerator(spark)
    
    try:
        if scenario == "good":
            test_scenario(
                "High Quality Data (Expected: PASS)", 
                data_generator.create_good_quality_data,
                "PASS",
                spark
            )
        elif scenario == "poor":
            test_scenario(
                "Poor Quality Data (Expected: WARNING/FAIL)",
                data_generator.create_poor_quality_data, 
                "WARNING",
                spark
            )
        elif scenario == "bad":
            test_scenario(
                "Corrupted Data (Expected: FAIL/QUARANTINE)",
                data_generator.create_corrupted_data,
                "FAIL",
                spark
            )
        else:
            print(f"❌ Unknown scenario: {scenario}")
            print("Available scenarios: good, poor, bad")
            return False
            
        print(f"\n✅ Scenario '{scenario}' completed successfully!")
        return True
        
    finally:
        spark.stop()


def check_dependencies():
    """Check if required dependencies are available"""
    
    print("🔍 Checking dependencies...")
    
    try:
        import pyspark
        print(f"✅ PySpark {pyspark.__version__} found")
    except ImportError:
        print("❌ PySpark not found. Install with: pip install pyspark")
        return False
    
    try:
        import delta
        print("✅ Delta Lake found")
    except ImportError:
        print("❌ Delta Lake not found. Install with: pip install delta-spark")
        return False
    
    try:
        import pydantic
        print(f"✅ Pydantic found")
    except ImportError:
        print("❌ Pydantic not found. Install with: pip install pydantic")
        return False
    
    # Check if framework modules are available
    try:
        from src.data_testing_framework import DataTestingFramework
        print("✅ Data Testing Framework modules found")
    except ImportError as e:
        print(f"❌ Framework modules not found: {e}")
        print("Make sure you're running from the project root directory")
        return False
    
    print("✅ All dependencies check passed!")
    return True


def show_usage_examples():
    """Show usage examples"""
    
    print("\n📚 USAGE EXAMPLES:")
    print("="*50)
    
    print("\n1. Test with good quality data (should PASS):")
    print("   python run_tests.py --scenario good")
    
    print("\n2. Test with poor quality data (should WARNING/FAIL):")
    print("   python run_tests.py --scenario poor")
    
    print("\n3. Test with corrupted data (should FAIL/QUARANTINE):")
    print("   python run_tests.py --scenario bad")
    
    print("\n4. Run all test scenarios:")
    print("   python run_tests.py")
    
    print("\n5. Enable email notifications (requires SMTP config):")
    print("   python run_tests.py --email")
    
    print("\n📋 WHAT EACH TEST DOES:")
    print("-" * 30)
    print("• Good Quality: Creates clean data with 95%+ quality scores")
    print("• Poor Quality: Introduces missing values, duplicates, old timestamps")
    print("• Corrupted: Severe issues - 50% missing data, invalid values, very old data")
    
    print("\n🎯 EXPECTED OUTCOMES:")
    print("-" * 30)
    print("• PASS: Data quality meets all standards (≥95% overall score)")
    print("• WARNING: Data quality acceptable but needs monitoring (85-94%)")
    print("• FAIL: Data quality below standards (<85%)")
    print("• QUARANTINE: Critical issues detected, data isolated")
    print("• EMERGENCY: Severe problems requiring immediate attention")


def main():
    """Main function"""
    
    parser = argparse.ArgumentParser(
        description="Test the Data Testing Framework with sample data",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_tests.py                    # Run all scenarios
  python run_tests.py --scenario good    # Test good quality data
  python run_tests.py --scenario poor    # Test poor quality data  
  python run_tests.py --scenario bad     # Test corrupted data
  python run_tests.py --help-examples    # Show detailed examples
        """
    )
    
    parser.add_argument(
        "--scenario", 
        choices=["good", "poor", "bad"],
        help="Run a specific test scenario"
    )
    
    parser.add_argument(
        "--email",
        action="store_true",
        help="Enable email notifications (requires SMTP configuration)"
    )
    
    parser.add_argument(
        "--help-examples",
        action="store_true", 
        help="Show detailed usage examples"
    )
    
    parser.add_argument(
        "--check-deps",
        action="store_true",
        help="Check if all dependencies are installed"
    )
    
    args = parser.parse_args()
    
    # Handle special flags
    if args.help_examples:
        show_usage_examples()
        return
    
    if args.check_deps:
        success = check_dependencies()
        sys.exit(0 if success else 1)
    
    # Check dependencies before running tests
    if not check_dependencies():
        print("\n❌ Dependency check failed. Please install missing dependencies.")
        sys.exit(1)
    
    # Run tests
    try:
        if args.scenario:
            # Run single scenario
            success = run_single_scenario(args.scenario, args.email)
            if success:
                print(f"\n🎉 Test scenario '{args.scenario}' completed!")
                print("💡 Try other scenarios or run all tests with: python run_tests.py")
            else:
                sys.exit(1)
        else:
            # Run all scenarios
            print("🧪 Running comprehensive test suite...")
            run_comprehensive_tests()
            
    except KeyboardInterrupt:
        print("\n\n⏹️  Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
